import axios from 'axios'
import * as fs from 'fs'

const TEMPLATES_ENDPOINT = 'https://api.madexthd.com/v2/materials/templates'
const OUTPUT_FILE_PATH = '../../src/main/resources/data/designTemplates.json'

interface Pagination {
  currentPage: number
  perPage: number
  total: number
  lastPage: number
  to: number
  from: number
}

interface DesignTemplate {
  name: string
  description: string
  templateBathroomFull: {
    faucet?: { id: string } | null
    floorTile?: { id: string } | null
    lighting?: { id: string } | null
    mirror?: { id: string } | null
    paint?: { id: string } | null
    showerSystem?: { id: string } | null
    showerWallTile?: { id: string } | null
    toilet?: { id: string } | null
    vanity?: { id: string } | null
    wallTile?: { id: string } | null
    shelves?: { id: string } | null
    tub?: { id: string } | null
  }
}

async function fetchTemplatesPage(
  currentPage: number,
): Promise<{ templates: DesignTemplate[]; pagination: Pagination }> {
  try {
    const response = await axios.get(TEMPLATES_ENDPOINT, { params: { currentPage } })
    const data = response.data.data.map((template: any) => ({
      name: template.name,
      description: template.description,
      faucet: template.templateBathroomFull.faucet?.id || null,
      floorTile: template.templateBathroomFull.floorTile?.id || null,
      lighting: template.templateBathroomFull.lighting?.id || null,
      mirror: template.templateBathroomFull.mirror?.id || null,
      paint: template.templateBathroomFull.paint?.id || null,
      showerSystem: template.templateBathroomFull.showerSystem?.id || null,
      showerWallTile: template.templateBathroomFull.showerWallTile?.id || null,
      toilet: template.templateBathroomFull.toilet?.id || null,
      vanity: template.templateBathroomFull.vanity?.id || null,
      wallTile: template.templateBathroomFull.wallTile?.id || null,
      shelves: template.templateBathroomFull.shelves?.id || null,
      tub: template.templateBathroomFull.tub?.id || null,
    }))
    const pagination: Pagination = response.data.pagination
    return { templates: data, pagination }
  } catch (error) {
    console.error(`Failed to fetch design templates for page ${currentPage}:`, error)
    throw error
  }
}

async function main() {
  try {
    const { templates, pagination } = await fetchTemplatesPage(1)
    let allTemplates = templates

    for (let currentPage = 2; currentPage <= pagination.lastPage; currentPage++) {
      const { templates } = await fetchTemplatesPage(currentPage)
      allTemplates = allTemplates.concat(templates)
    }

    fs.writeFileSync(OUTPUT_FILE_PATH, JSON.stringify(allTemplates, null, 2))
    console.log(`Saved ${allTemplates.length} templates to ${OUTPUT_FILE_PATH}`)
  } catch (error) {
    console.error('Failed to fetch and save design templates:', error)
  }
}

main()
