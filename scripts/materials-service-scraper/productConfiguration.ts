import { ProductItem } from './materialsService'

const defaultValuesToExtract = ['id']
export const defaultFilePath = '../../src/main/resources/data/'
export const defaultFileName = 'productData.json'
const defaultFilteringRule: FilterRule = () => {
  return true
}

type FilterRule = (product: ProductItem) => boolean

type ProductConfigEntry = {
  endpoint: string
  filteringRule: FilterRule
  valuesToExtract: string[]
  filename?: string
}

type ProductConfiguration = Record<string, ProductConfigEntry>

const excludedMirrorIds = [
  '58064c9e-c2ae-44a6-ae1f-9ce6030b7125',
  'fb263516-00cd-4d87-8407-53853644bbbb',
  '19582f50-5c66-459d-9eca-0ccf43d971ef',
  '2735b54a-5947-4d71-9ff0-b35fca858faa',
  'cecd9b90-5e3b-458d-b3ce-7fc208b3d08b',
  '651e69e4-e6ed-47fa-b06e-0220aeeafd04',
]

export const productConfig: ProductConfiguration = {
  faucet: {
    endpoint: 'faucets',
    filteringRule: defaultFilteringRule,
    valuesToExtract: defaultValuesToExtract,
  },
  floorTile: {
    endpoint: 'tiles',
    filteringRule: (product) => product.availableForFloor === true,
    valuesToExtract: defaultValuesToExtract,
  },
  lighting: {
    endpoint: 'lightings',
    filteringRule: (product) => product.abovePlacementId !== null,
    valuesToExtract: defaultValuesToExtract,
  },
  mirror: {
    endpoint: 'mirrors',
    filteringRule: (product) => !excludedMirrorIds.includes(product.id),
    valuesToExtract: [...defaultValuesToExtract, 'length'],
    filename: defaultFilePath + 'mirrorData.json',
  },
  paint: {
    endpoint: 'paints',
    filteringRule: defaultFilteringRule,
    valuesToExtract: defaultValuesToExtract,
  },
  showerGlass: {
    endpoint: 'shower-glasses',
    filteringRule: defaultFilteringRule,
    valuesToExtract: defaultValuesToExtract,
  },
  showerSystem: {
    endpoint: 'shower-systems',
    filteringRule: defaultFilteringRule,
    valuesToExtract: defaultValuesToExtract,
  },
  showerWallTile: {
    endpoint: 'tiles',
    filteringRule: (product) => product.availableForShowerWall === true,
    valuesToExtract: defaultValuesToExtract,
  },
  toilet: {
    endpoint: 'toilets',
    filteringRule: defaultFilteringRule,
    valuesToExtract: defaultValuesToExtract,
  },
  vanity: {
    endpoint: 'vanities',
    filteringRule: defaultFilteringRule,
    valuesToExtract: [...defaultValuesToExtract, 'length'],
    filename: defaultFilePath + 'vanityData.json',
  },
  wallTile: {
    endpoint: 'tiles',
    filteringRule: (product) => product.availableForWall === true,
    valuesToExtract: defaultValuesToExtract,
  },
  shelves: {
    endpoint: 'shelves',
    filteringRule: defaultFilteringRule,
    valuesToExtract: defaultValuesToExtract,
  },
  tub: {
    endpoint: 'tubs',
    filteringRule: (product) => product.tubType === 'ALCOVE',
    valuesToExtract: defaultValuesToExtract,
  },
}
