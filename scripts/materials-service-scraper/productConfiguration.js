'use strict'
var __spreadArray =
  (this && this.__spreadArray) ||
  function (to, from, pack) {
    if (pack || arguments.length === 2)
      for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
          if (!ar) ar = Array.prototype.slice.call(from, 0, i)
          ar[i] = from[i]
        }
      }
    return to.concat(ar || Array.prototype.slice.call(from))
  }
Object.defineProperty(exports, '__esModule', { value: true })
exports.productConfig = exports.defaultFileName = exports.defaultFilePath = void 0
var defaultValuesToExtract = ['id']
exports.defaultFilePath = '../../src/main/resources/data/'
exports.defaultFileName = 'productData.json'
var defaultFilteringRule = function () {
  return true
}
var excludedMirrorIds = [
  '58064c9e-c2ae-44a6-ae1f-9ce6030b7125',
  'fb263516-00cd-4d87-8407-53853644bbbb',
  '19582f50-5c66-459d-9eca-0ccf43d971ef',
  '2735b54a-5947-4d71-9ff0-b35fca858faa',
  'cecd9b90-5e3b-458d-b3ce-7fc208b3d08b',
  '651e69e4-e6ed-47fa-b06e-0220aeeafd04',
]
exports.productConfig = {
  faucet: {
    endpoint: 'faucets',
    filteringRule: defaultFilteringRule,
    valuesToExtract: defaultValuesToExtract,
  },
  floorTile: {
    endpoint: 'tiles',
    filteringRule: function (product) {
      return product.availableForFloor === true
    },
    valuesToExtract: defaultValuesToExtract,
  },
  lighting: {
    endpoint: 'lightings',
    filteringRule: function (product) {
      return product.abovePlacementId !== null
    },
    valuesToExtract: defaultValuesToExtract,
  },
  mirror: {
    endpoint: 'mirrors',
    filteringRule: function (product) {
      return !excludedMirrorIds.includes(product.id)
    },
    valuesToExtract: __spreadArray(__spreadArray([], defaultValuesToExtract, true), ['length'], false),
    filename: exports.defaultFilePath + 'mirrorData.json',
  },
  paint: {
    endpoint: 'paints',
    filteringRule: defaultFilteringRule,
    valuesToExtract: defaultValuesToExtract,
  },
  showerGlass: {
    endpoint: 'shower-glasses',
    filteringRule: defaultFilteringRule,
    valuesToExtract: defaultValuesToExtract,
  },
  showerSystem: {
    endpoint: 'shower-systems',
    filteringRule: defaultFilteringRule,
    valuesToExtract: defaultValuesToExtract,
  },
  showerWallTile: {
    endpoint: 'tiles',
    filteringRule: function (product) {
      return product.availableForShowerWall === true
    },
    valuesToExtract: defaultValuesToExtract,
  },
  toilet: {
    endpoint: 'toilets',
    filteringRule: defaultFilteringRule,
    valuesToExtract: defaultValuesToExtract,
  },
  vanity: {
    endpoint: 'vanities',
    filteringRule: defaultFilteringRule,
    valuesToExtract: __spreadArray(__spreadArray([], defaultValuesToExtract, true), ['length'], false),
    filename: exports.defaultFilePath + 'vanityData.json',
  },
  wallTile: {
    endpoint: 'tiles',
    filteringRule: function (product) {
      return product.availableForWall === true
    },
    valuesToExtract: defaultValuesToExtract,
  },
  shelves: {
    endpoint: 'shelves',
    filteringRule: defaultFilteringRule,
    valuesToExtract: defaultValuesToExtract,
  },
  tub: {
    endpoint: 'tubs',
    filteringRule: function (product) {
      return product.tubType === 'ALCOVE'
    },
    valuesToExtract: defaultValuesToExtract,
  },
}
