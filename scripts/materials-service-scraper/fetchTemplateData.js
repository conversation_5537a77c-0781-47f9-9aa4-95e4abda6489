'use strict'
var __awaiter =
  (this && this.__awaiter) ||
  function (thisArg, _arguments, P, generator) {
    function adopt(value) {
      return value instanceof P
        ? value
        : new P(function (resolve) {
            resolve(value)
          })
    }
    return new (P || (P = Promise))(function (resolve, reject) {
      function fulfilled(value) {
        try {
          step(generator.next(value))
        } catch (e) {
          reject(e)
        }
      }
      function rejected(value) {
        try {
          step(generator['throw'](value))
        } catch (e) {
          reject(e)
        }
      }
      function step(result) {
        result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected)
      }
      step((generator = generator.apply(thisArg, _arguments || [])).next())
    })
  }
var __generator =
  (this && this.__generator) ||
  function (thisArg, body) {
    var _ = {
        label: 0,
        sent: function () {
          if (t[0] & 1) throw t[1]
          return t[1]
        },
        trys: [],
        ops: [],
      },
      f,
      y,
      t,
      g
    return (
      (g = { next: verb(0), throw: verb(1), return: verb(2) }),
      typeof Symbol === 'function' &&
        (g[Symbol.iterator] = function () {
          return this
        }),
      g
    )
    function verb(n) {
      return function (v) {
        return step([n, v])
      }
    }
    function step(op) {
      if (f) throw new TypeError('Generator is already executing.')
      while ((g && ((g = 0), op[0] && (_ = 0)), _))
        try {
          if (
            ((f = 1),
            y &&
              (t = op[0] & 2 ? y['return'] : op[0] ? y['throw'] || ((t = y['return']) && t.call(y), 0) : y.next) &&
              !(t = t.call(y, op[1])).done)
          )
            return t
          if (((y = 0), t)) op = [op[0] & 2, t.value]
          switch (op[0]) {
            case 0:
            case 1:
              t = op
              break
            case 4:
              _.label++
              return { value: op[1], done: false }
            case 5:
              _.label++
              y = op[1]
              op = [0]
              continue
            case 7:
              op = _.ops.pop()
              _.trys.pop()
              continue
            default:
              if (!((t = _.trys), (t = t.length > 0 && t[t.length - 1])) && (op[0] === 6 || op[0] === 2)) {
                _ = 0
                continue
              }
              if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) {
                _.label = op[1]
                break
              }
              if (op[0] === 6 && _.label < t[1]) {
                _.label = t[1]
                t = op
                break
              }
              if (t && _.label < t[2]) {
                _.label = t[2]
                _.ops.push(op)
                break
              }
              if (t[2]) _.ops.pop()
              _.trys.pop()
              continue
          }
          op = body.call(thisArg, _)
        } catch (e) {
          op = [6, e]
          y = 0
        } finally {
          f = t = 0
        }
      if (op[0] & 5) throw op[1]
      return { value: op[0] ? op[1] : void 0, done: true }
    }
  }
Object.defineProperty(exports, '__esModule', { value: true })
var axios_1 = require('axios')
var fs = require('fs')
var TEMPLATES_ENDPOINT = 'https://api.madexthd.com/v2/materials/templates'
var OUTPUT_FILE_PATH = '../../src/main/resources/data/designTemplates.json'
function fetchTemplatesPage(currentPage) {
  return __awaiter(this, void 0, void 0, function () {
    var response, data, pagination, error_1
    return __generator(this, function (_a) {
      switch (_a.label) {
        case 0:
          _a.trys.push([0, 2, , 3])
          return [4 /*yield*/, axios_1.default.get(TEMPLATES_ENDPOINT, { params: { currentPage: currentPage } })]
        case 1:
          response = _a.sent()
          data = response.data.data.map(function (template) {
            var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m
            return {
              name: template.name,
              description: template.description,
              faucet: ((_a = template.templateBathroomFull.faucet) === null || _a === void 0 ? void 0 : _a.id) || null,
              floorTile:
                ((_b = template.templateBathroomFull.floorTile) === null || _b === void 0 ? void 0 : _b.id) || null,
              lighting:
                ((_c = template.templateBathroomFull.lighting) === null || _c === void 0 ? void 0 : _c.id) || null,
              mirror: ((_d = template.templateBathroomFull.mirror) === null || _d === void 0 ? void 0 : _d.id) || null,
              paint: ((_e = template.templateBathroomFull.paint) === null || _e === void 0 ? void 0 : _e.id) || null,
              showerSystem:
                ((_f = template.templateBathroomFull.showerSystem) === null || _f === void 0 ? void 0 : _f.id) || null,
              showerWallTile:
                ((_g = template.templateBathroomFull.showerWallTile) === null || _g === void 0 ? void 0 : _g.id) ||
                null,
              toilet: ((_h = template.templateBathroomFull.toilet) === null || _h === void 0 ? void 0 : _h.id) || null,
              vanity: ((_j = template.templateBathroomFull.vanity) === null || _j === void 0 ? void 0 : _j.id) || null,
              wallTile:
                ((_k = template.templateBathroomFull.wallTile) === null || _k === void 0 ? void 0 : _k.id) || null,
              shelves:
                ((_l = template.templateBathroomFull.shelves) === null || _l === void 0 ? void 0 : _l.id) || null,
              tub: ((_m = template.templateBathroomFull.tub) === null || _m === void 0 ? void 0 : _m.id) || null,
            }
          })
          pagination = response.data.pagination
          return [2 /*return*/, { templates: data, pagination: pagination }]
        case 2:
          error_1 = _a.sent()
          console.error('Failed to fetch design templates for page '.concat(currentPage, ':'), error_1)
          throw error_1
        case 3:
          return [2 /*return*/]
      }
    })
  })
}
function main() {
  return __awaiter(this, void 0, void 0, function () {
    var _a, templates, pagination, allTemplates, currentPage, templates_1, error_2
    return __generator(this, function (_b) {
      switch (_b.label) {
        case 0:
          _b.trys.push([0, 6, , 7])
          return [4 /*yield*/, fetchTemplatesPage(1)]
        case 1:
          ;(_a = _b.sent()), (templates = _a.templates), (pagination = _a.pagination)
          allTemplates = templates
          currentPage = 2
          _b.label = 2
        case 2:
          if (!(currentPage <= pagination.lastPage)) return [3 /*break*/, 5]
          return [4 /*yield*/, fetchTemplatesPage(currentPage)]
        case 3:
          templates_1 = _b.sent().templates
          allTemplates = allTemplates.concat(templates_1)
          _b.label = 4
        case 4:
          currentPage++
          return [3 /*break*/, 2]
        case 5:
          fs.writeFileSync(OUTPUT_FILE_PATH, JSON.stringify(allTemplates, null, 2))
          console.log('Saved '.concat(allTemplates.length, ' templates to ').concat(OUTPUT_FILE_PATH))
          return [3 /*break*/, 7]
        case 6:
          error_2 = _b.sent()
          console.error('Failed to fetch and save design templates:', error_2)
          return [3 /*break*/, 7]
        case 7:
          return [2 /*return*/]
      }
    })
  })
}
main()
