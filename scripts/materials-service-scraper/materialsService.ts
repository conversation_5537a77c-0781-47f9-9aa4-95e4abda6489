import fetch from 'node-fetch'

const BASE_URL = 'https://api.madexthd.com/v2/materials/products/renderable-products/'

export interface ProductItem {
  id: string
  tubType?: string
  abovePlacementId?: string | null
  availableForFloor?: boolean
  availableForShowerWall?: boolean
  availableForWall?: boolean
  length?: string
}

interface PaginationData {
  currentPage: number
  lastPage: number
}

export interface ApiResponse {
  data: ProductItem[]
  pagination: PaginationData
}

type FilterRule = (product: ProductItem) => boolean

export const fetchProductDataForType = async (config: {
  endpoint: string
  filteringRule: FilterRule
  valuesToExtract: string[]
}) => {
  let currentPage = 1
  let lastPage = 1
  const productData: Partial<ProductItem>[] = []
  const { endpoint, filteringRule, valuesToExtract } = config

  do {
    try {
      const response = await fetch(`${BASE_URL}${endpoint}?currentPage=${currentPage}`, {
        method: 'GET',
        headers: {
          Accept: 'application/json',
        },
      })
      const data = (await response.json()) as ApiResponse

      if (!response.ok) {
        console.log(
          `Error fetching data for ${endpoint} on page ${currentPage}. Status: ${response.status}. Status Text: ${response.statusText}`,
        )
        return productData
      }

      if (!data.data) {
        console.log(`Unexpected response structure for ${endpoint} on page ${currentPage}:`, data)
      }

      let filteredProducts = data.data.filter(filteringRule)

      const items = filteredProducts.map((product) => {
        const result: Record<string, string | boolean | null | undefined> = {}
        for (const value of valuesToExtract) {
          result[value] = product[value]
        }
        return result
      })

      productData.push(...items)

      currentPage = data.pagination.currentPage + 1
      lastPage = data.pagination.lastPage
    } catch (error) {
      console.error(`Error fetching data for ${endpoint} on page ${currentPage}:`, error)
      break
    }
  } while (currentPage <= lastPage)

  return productData
}
