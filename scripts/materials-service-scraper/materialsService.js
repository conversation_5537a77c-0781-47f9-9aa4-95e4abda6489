'use strict'
var __awaiter =
  (this && this.__awaiter) ||
  function (thisArg, _arguments, P, generator) {
    function adopt(value) {
      return value instanceof P
        ? value
        : new P(function (resolve) {
            resolve(value)
          })
    }
    return new (P || (P = Promise))(function (resolve, reject) {
      function fulfilled(value) {
        try {
          step(generator.next(value))
        } catch (e) {
          reject(e)
        }
      }
      function rejected(value) {
        try {
          step(generator['throw'](value))
        } catch (e) {
          reject(e)
        }
      }
      function step(result) {
        result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected)
      }
      step((generator = generator.apply(thisArg, _arguments || [])).next())
    })
  }
var __generator =
  (this && this.__generator) ||
  function (thisArg, body) {
    var _ = {
        label: 0,
        sent: function () {
          if (t[0] & 1) throw t[1]
          return t[1]
        },
        trys: [],
        ops: [],
      },
      f,
      y,
      t,
      g
    return (
      (g = { next: verb(0), throw: verb(1), return: verb(2) }),
      typeof Symbol === 'function' &&
        (g[Symbol.iterator] = function () {
          return this
        }),
      g
    )
    function verb(n) {
      return function (v) {
        return step([n, v])
      }
    }
    function step(op) {
      if (f) throw new TypeError('Generator is already executing.')
      while ((g && ((g = 0), op[0] && (_ = 0)), _))
        try {
          if (
            ((f = 1),
            y &&
              (t = op[0] & 2 ? y['return'] : op[0] ? y['throw'] || ((t = y['return']) && t.call(y), 0) : y.next) &&
              !(t = t.call(y, op[1])).done)
          )
            return t
          if (((y = 0), t)) op = [op[0] & 2, t.value]
          switch (op[0]) {
            case 0:
            case 1:
              t = op
              break
            case 4:
              _.label++
              return { value: op[1], done: false }
            case 5:
              _.label++
              y = op[1]
              op = [0]
              continue
            case 7:
              op = _.ops.pop()
              _.trys.pop()
              continue
            default:
              if (!((t = _.trys), (t = t.length > 0 && t[t.length - 1])) && (op[0] === 6 || op[0] === 2)) {
                _ = 0
                continue
              }
              if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) {
                _.label = op[1]
                break
              }
              if (op[0] === 6 && _.label < t[1]) {
                _.label = t[1]
                t = op
                break
              }
              if (t && _.label < t[2]) {
                _.label = t[2]
                _.ops.push(op)
                break
              }
              if (t[2]) _.ops.pop()
              _.trys.pop()
              continue
          }
          op = body.call(thisArg, _)
        } catch (e) {
          op = [6, e]
          y = 0
        } finally {
          f = t = 0
        }
      if (op[0] & 5) throw op[1]
      return { value: op[0] ? op[1] : void 0, done: true }
    }
  }
Object.defineProperty(exports, '__esModule', { value: true })
exports.fetchProductDataForType = void 0
var node_fetch_1 = require('node-fetch')
var BASE_URL = 'https://api.madexthd.com/v2/materials/products/renderable-products/'
var fetchProductDataForType = function (config) {
  return __awaiter(void 0, void 0, void 0, function () {
    var currentPage,
      lastPage,
      productData,
      endpoint,
      filteringRule,
      valuesToExtract,
      response,
      data,
      filteredProducts,
      items,
      error_1
    return __generator(this, function (_a) {
      switch (_a.label) {
        case 0:
          currentPage = 1
          lastPage = 1
          productData = []
          ;(endpoint = config.endpoint),
            (filteringRule = config.filteringRule),
            (valuesToExtract = config.valuesToExtract)
          _a.label = 1
        case 1:
          _a.trys.push([1, 4, , 5])
          return [
            4 /*yield*/,
            (0, node_fetch_1.default)(''.concat(BASE_URL).concat(endpoint, '?currentPage=').concat(currentPage), {
              method: 'GET',
              headers: {
                Accept: 'application/json',
              },
            }),
          ]
        case 2:
          response = _a.sent()
          return [4 /*yield*/, response.json()]
        case 3:
          data = _a.sent()
          if (!response.ok) {
            console.log(
              'Error fetching data for '
                .concat(endpoint, ' on page ')
                .concat(currentPage, '. Status: ')
                .concat(response.status, '. Status Text: ')
                .concat(response.statusText),
            )
            return [2 /*return*/, productData]
          }
          if (!data.data) {
            console.log(
              'Unexpected response structure for '.concat(endpoint, ' on page ').concat(currentPage, ':'),
              data,
            )
          }
          filteredProducts = data.data.filter(filteringRule)
          items = filteredProducts.map(function (product) {
            var result = {}
            for (var _i = 0, valuesToExtract_1 = valuesToExtract; _i < valuesToExtract_1.length; _i++) {
              var value = valuesToExtract_1[_i]
              result[value] = product[value]
            }
            return result
          })
          productData.push.apply(productData, items)
          currentPage = data.pagination.currentPage + 1
          lastPage = data.pagination.lastPage
          return [3 /*break*/, 5]
        case 4:
          error_1 = _a.sent()
          console.error('Error fetching data for '.concat(endpoint, ' on page ').concat(currentPage, ':'), error_1)
          return [3 /*break*/, 6]
        case 5:
          if (currentPage <= lastPage) return [3 /*break*/, 1]
          _a.label = 6
        case 6:
          return [2 /*return*/, productData]
      }
    })
  })
}
exports.fetchProductDataForType = fetchProductDataForType
