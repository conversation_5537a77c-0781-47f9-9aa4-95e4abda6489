import * as fs from 'fs'
import { ProductItem, fetchProductDataForType } from './materialsService'
import { productConfig, defaultFileName, defaultFilePath } from './productConfiguration'

const fetchProductData = async () => {
  const defaultProductDataStore: Record<string, Partial<ProductItem>[]> = {}

  for (const [key, config] of Object.entries(productConfig)) {
    const productData = await fetchProductDataForType(config)

    if (config.filename) {
      fs.writeFileSync(config.filename, JSON.stringify(productData, null, 2))
    } else {
      defaultProductDataStore[key] = productData
    }
  }

  fs.writeFileSync(defaultFilePath + defaultFileName, JSON.stringify(defaultProductDataStore, null, 2))
}

fetchProductData()
