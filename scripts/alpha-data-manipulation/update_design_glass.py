import os
import json
import requests

api_endpoint = "https://averyapi.com/projects"
filepath = os.path.dirname(__file__) + "/designs.json"

headers = {"Content-Type": "application/json"}

def main():
    res = requests.get(api_endpoint)
    projects = res.json()
    failed = []
    with open(filepath) as file:
        designs = json.load(file)

        for p in projects:
            project_endpoint = f"{api_endpoint}/{p["id"]}"

            try:
              projectdata = requests.get(project_endpoint).json()
            except:
                print(f"Failed to load {p["id"]}")
                failed.append(p["id"])

            if "designs" not in projectdata or not projectdata["designs"]: 
                continue
            
            objects: list = projectdata["scan"]["objects"]
            shower  = next(filter(lambda x: "shower" in x["category"], objects), None)
            bathtub = next(filter(lambda x: "bathtub" in x["category"], objects), None)

            tub_has_shower = shower is None
            shower_area = bathtub if tub_has_shower else shower

            if not shower_area: 
                continue
            
            dims = shower_area["dimensions"]
            # Consider only x and z axis (do not consider height)
            # Also convert to inches
            longest_side = max(dims[0], dims[2]) * 39.3701

            for design in projectdata["designs"]:
                curr_design = next(filter(lambda x: x["id"] == design["id"], designs), None)
                if not curr_design:
                    continue

                if longest_side < 56:
                    tubdoor = None
                    showerglass = None
                elif longest_side <= 59.625:
                    if "tubDoorSliding" in curr_design:
                        tubdoor = curr_design["tubDoorSliding"]
                    if "showerGlassSliding" in curr_design:
                        showerglass = curr_design["showerGlassSliding"]
                else:
                    if "tubDoorFixed" in curr_design: 
                        tubdoor = curr_design["tubDoorFixed"]
                    if "showerGlassFixed" in curr_design: 
                        showerglass = curr_design["showerGlassFixed"]
                
                design["tubDoor"] = tubdoor
                design["showerGlass"] = showerglass

            print(f"Upading project {projectdata["id"]} -> {longest_side}")
            requests.put(project_endpoint, data=json.dumps(projectdata), headers=headers)

    print(failed)

if __name__ == "__main__":
    main()
