import os
import requests
import json

api_endpoint = "https://averyapi.com/projects"
filepath = os.path.dirname(__file__) + "/format_pricing.csv"
headers = {"Content-Type": "application/json"}


def get_modified_design(design_data):
    price: str = design_data["price"]
    if "US" in price:
        splitted_price = price.split("\xa0")
        if len(splitted_price) > 1:
            price = f"${splitted_price[0]},{splitted_price[1]}"
        else:
            price = price.replace("US", "").replace(".", ",")

    design_data["price"] = price
    design_data["totalPrice"] = int(price.replace("$", "").replace(",", "")) * 100

    return design_data


def update_project(endpoint, project_data):
    requests.put(endpoint, json=project_data, headers=headers)
    print(f"Product {endpoint} description updated")


def main():
    res = requests.get(api_endpoint)
    projects = res.json()

    if os.path.exists(filepath):
        os.remove(filepath)

    for project in projects:
        endpoint = f'{api_endpoint}/{project["id"]}'
        project_res = requests.get(endpoint)
        project_data = project_res.json()

        if "designs" not in project_data or not project_data["designs"]:
            continue

        modified_designs = []
        for design in project_data["designs"]:
            if "price" in design:
                design = get_modified_design(design)

            modified_designs.append(design)

        project_data["designs"] = modified_designs
        update_project(endpoint, project_data)

    print("All product descriptions updated")


if __name__ == "__main__":
    main()
