import csv
import json
import requests

# Define the API endpoint and headers
# VENDOR_ID = "82df69d2-02e1-4d08-879b-cb50a2adc492"  # Build.com
VENDOR_ID = "1ae358c8-bb58-4b16-ab2c-8415ace483d1"  # Bedrosians.com
API_ENDPOINT = f"https://api.averyapi.com/v2/materials/vendors/{VENDOR_ID}/products"
HEADERS = {
    "Content-Type": "application/json",
}


def convert_price(price_str):
    if price_str:
        return int(float(price_str.replace("$", "").replace(",", "")) * 100)
    return None


def process_csv(input_csv):
    with open(input_csv, mode="r", newline="", encoding="utf-8") as csvfile:
        reader = csv.DictReader(csvfile)
        for row in reader:
            uuid = row["uuid"]
            price = convert_price(row["price"])
            cart_price = convert_price(row["cart_price"])

            endpoint = f"{API_ENDPOINT}/{uuid}"
            try:
                data = requests.get(endpoint).json()["data"][0]
            except:
                print(f"Product not found, failed to update {endpoint}")
                continue

            for k in [
                "id",
                "name",
                "modelNumber",
                "preferredVendor",
                "category",
                "brand",
                "productFamilyId",
                "variants",
                "featuredImage",
                "images",
                "availableFrom",
                "isGCSupplied",
            ]:
                if k in data:
                    del data[k]

            if price is None:
                data["availability"] = "DISCONTINUED"
            elif price == 0:
                data["availability"] = "OUT_OF_STOCK"
            else:
                data["price"] = price
                data["salePrice"] = price
                data["ourPrice"] = cart_price

            data["setAsPreferredVendor"] = True

            response = requests.put(endpoint, json=data, headers=HEADERS)
            if response.status_code != 204:
                print(f"Failed to update product {uuid}: {response.status_code} {response.text}")
            else:
                print(f"Successfully updated product {uuid}")


if __name__ == "__main__":
    # input_csv = "build_pricing.csv"
    input_csv = "bedrosians_pricing.csv"
    process_csv(input_csv)
