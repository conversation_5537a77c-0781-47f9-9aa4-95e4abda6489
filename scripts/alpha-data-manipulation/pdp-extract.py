import json
import csv

ids_file_path = 'pdp_ids.txt'

with open(ids_file_path, 'r') as file:
    ids = [line.strip() for line in file.readlines()]

with open('../../src/main/resources/data/productDetails.json', 'r', encoding='utf-8') as file:
    data = json.load(file)

# Open a CSV file to write the results
with open('light_count.csv', 'w', newline='', encoding='utf-8') as csv_file:
    writer = csv.writer(csv_file)

    # Write the header row
    writer.writerow(['id', 'numberOfLights'])

    # Iterate over each product
    for product in data:
        # Check if the product's ID is in the list of target IDs
        if product['id'] in ids:
            # Initialize variable to hold the number of lights (default to None)
            lights = None
            # Loop through each specification group
            for spec_group in product.get('specificationGroup', []):
                # Loop through each specification
                for spec in spec_group.get('specifications', []):
                    # Check if the specification name is "Number Of Basins"
                    if spec.get('specName') == "Number of Light Source(s)":
                        # Set the lights variable to the spec value
                        lights = spec.get('specValue')
                        # Since we found the lights, we can break the inner loop
                        break
                # If sinks were found, break the outer loop as well
                if lights is not None:
                    break
            # Write the product ID and the number of lights to the CSV file
            # If lights were not found, it will write None
            writer.writerow([product['id'], lights])
