import csv
import requests

materials_api_endpoint = "https://api.averyapi.com/v2/materials/products"
pdp_api_endpoint = "https://averyapi.com/products"

missing_pdp = [
    "666bf15a-93ba-4465-972e-59d2131a172e",
    "ef88fac0-28fb-4b23-afed-c5e304356de8",
    "acd88e3b-3f58-4a33-8be0-86f6e8cb95a3",
    "63ffe3b7-df7a-4439-a042-80a83625b571",
    "bd1026c9-0044-403d-aef8-e9daff8b6b95",
    "96cefb21-2534-40e8-9ec9-2d7c90172cb9",
    "ad56f0b2-2e0a-4295-85da-bc0dd8deba3a",
    "f7fb4e6a-6cde-484f-a7b8-dcf00583f4e9",
    "cd5b0548-2a71-4e17-ac61-45a244c24186",
    "d8cfbb8b-c2a6-4bfd-a473-cf3a87ab4b1f",
    "1c6eaae1-c4e8-4323-a7c2-b7074b185fe0",
    "c880be1a-fd28-48a6-8bda-ba77f693ec00",
    "f8cf2f1c-02a7-4802-8a2d-dc8eb74f56ff",
    "97d986a6-d8f8-4f42-a04b-5f536f035304",
    "24e3733f-96a9-4279-b2b9-db47484e173e",
    "52e46cf6-5512-44c8-ad3d-310fcdedce76",
    "50922a47-4b7d-4b84-a419-98910b7929b9",
    "f0e8038f-c5d1-48ce-9b83-d9c37859fc2a",
    "be3e7f4d-ace2-443d-9bdc-a2601471d193",
    "3776e582-92d5-4e4a-9fe5-b77a0ac16c31",
    "aa44400a-c29f-4266-8f1e-769fa2e11e91",
    "bceb9005-7232-4bf1-8377-3ad30eb63956",
    "b910fd69-36e4-4d1f-88dc-04f76e8b5db3",
    "5104adcf-2f7c-40da-afb2-91a93798987a",
    "4f625122-64aa-4397-9351-6e5f779de228",
    "56cfbdf5-1f82-4723-bf33-ce357b1555af",
    "fd45982f-60f7-46d0-bd7d-b411ada26391",
    "292c810a-edab-4e5a-9fb2-77331e89c319",
    "f4bf18bb-1b78-4db2-a1fa-05b43623980f",
    "6985a414-0f61-4b36-91d2-e62817888cc7",
    "884cf4fb-5fa5-4806-ba44-45bc8f254a1d",
    "c2bdd2a8-efb0-4cb9-9b34-727cec06fa88",
]
# def main():
#     res = requests.get(materials_api_endpoint+ "?perPage=10000")
#     data = res.json()["data"]

#     result = []
#     failed = []
#     for product in data:
#         try:
#             pdp_endpoint = pdp_api_endpoint + "/" + product["id"]
#             pdpres = requests.get(pdp_endpoint)
#             pdpdata = pdpres.json()
#             result.append(pdpdata)
#         except:
#             failed.append(product["id"])

#     with open("result.json", "w") as f:
#         json.dump(result, f)
#     with open("failed.json", "w") as f:
#         json.dump(failed, f)


def main():
    res = requests.get(f"https://api.averyapi.com/v2/materials/products/{';'.join(missing_pdp)}?include[]=vendor_data")
    data = res.json()["data"]

    with open("missing_pdp2.csv", "w", newline="") as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow(["ID", "Type", "Name", "Vendor Link", "Description", "Quality", "Features"])
        for product in data:
            pdpres = requests.get(f"{pdp_api_endpoint}/{product["id"]}")
            pdpdata = pdpres.json()

            id = product["id"]
            type = product["category"]["name"]
            name = product["name"]
            url = product["availableFrom"][0]["link"]
            description = pdpdata["description"]

            writer.writerow([id, type, name, url, description, "", ""])


if __name__ == "__main__":
    main()
