import os
import json
import requests

API_ENDPOINT = "https://averyapi.com/v2/products/"
filepath = os.path.dirname(__file__) + "/productsToCreate.json"

headers = {"Content-Type": "application/json"}


def main():
    with open(filepath, encoding="utf8") as file:
        print(filepath)
        products = json.load(file)
        for product in products:
            uuid = product["id"]
            # print(f"Updating product {uuid}...")
            try:
                response = requests.put(
                    API_ENDPOINT + uuid,
                    data=json.dumps(product),
                    headers=headers,
                    timeout=3,
                )
                response.raise_for_status()
                print(f"Updated product {uuid}.")
            except requests.exceptions.HTTPError as e:
                if e.response.status_code == 404:
                    # print("Product not found; creating...")
                    response = requests.post(
                        API_ENDPOINT,
                        data=json.dumps(product),
                        headers=headers,
                        timeout=3,
                    )
                    response.raise_for_status()
                    # print("Created!")
                else:
                    raise e

        print("Successfully updated/created all products!")


if __name__ == "__main__":
    main()
