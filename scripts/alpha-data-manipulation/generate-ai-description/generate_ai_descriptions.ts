import dotenv from 'dotenv'
import path from 'path'
import fs from 'fs'
import axios from 'axios'
import { z } from 'zod'
import { zodResponseFormat } from 'openai/helpers/zod.js'
import OpenAI from 'openai'

dotenv.config({ path: path.resolve(__dirname, '../../../.env') })

const apiEndpoint = 'https://averyapi.com/v2/products'
const catalogEndpoint = 'https://api.averyapi.com/v2/materials/products'

const filepath = path.join(__dirname, 'product_ai_details.csv')

const DesignQuizResultResponse = z.object({
  aiDescription: z.string(),
  pros: z.string().array(),
  cons: z.string().array(),
})

const openai = new OpenAI()

const SYSTEM_INSTRUCTIONS = `You are a senior virtual interior design assistant with many years of experience. You are creating a key-features/considerations list and a short description for a catalog of bathroom products based on their manufacturer description.`

const PROMPT_PREFIX = `Step 1 - Review the name and description of the product and create a 3-6 word key-features/considerations list for the product, add only important key-features and considerations there's no minimun key-features/considerations amount required. Example of key-features: Quality materials, Elegant countertop, Ample storage. Example of considerations: Installation difficulty, Plumbing adjustments, Price Point.

Step 2 - Based on the name and description of the product, create a 150-200 character description of the product. Include the product category, design style, construction materials, color palette and a brief overview of the product's features. Example: This vanity hits your aesthetic preferences with its modern design, solid wood construction, and sleek neutral color palette, blending style and durability seamlessly.`

const buildPrompt = (promptPrefix: string, category: string, price: number, name: string, description: string) => {
  return `${promptPrefix}
  
  Product Category: ${category}
  Product Name: ${name}
  Product Description: ${description}
  Price: ${price}
  `
}

const generateAIDetails = async (category: string, price: number, name: string, description: string) => {
  const completion = await openai?.chat.completions.create({
    model: 'gpt-4o-2024-08-06',
    messages: [
      { role: 'system', content: SYSTEM_INSTRUCTIONS },
      {
        role: 'user',
        content: buildPrompt(PROMPT_PREFIX, category, price, name, description),
      },
    ],
    response_format: zodResponseFormat(DesignQuizResultResponse, 'GeneratedDetails'),
  })

  return completion?.choices[0]?.message?.content
}

// Ensure the CSV file has a header row
if (!fs.existsSync(filepath)) {
  fs.writeFileSync(filepath, 'ID,Details\n', { encoding: 'utf8' })
}

// Read existing IDs from the CSV file
const getExistingIds = (): Set<string> => {
  const existingIds = new Set<string>()

  if (fs.existsSync(filepath)) {
    const fileContent = fs.readFileSync(filepath, { encoding: 'utf8' })
    const rows = fileContent.split('\n')

    // Skip the header and iterate over rows
    rows.slice(1).forEach((row) => {
      const [id] = row.split(',', 1) // Get the ID
      if (id) existingIds.add(id.trim())
    })
  }

  return existingIds
}

// Append data to the CSV file
const appendToCSV = (productId: string, details: string) => {
  const row = `${productId},"${details.replace(/"/g, '""')}"\n` // Remove line breaks from details
  fs.appendFileSync(filepath, row, { encoding: 'utf8' })
}

async function main(): Promise<void> {
  try {
    const existingIds = getExistingIds() // Load existing IDs

    const {
      data: { data: catalog },
    } = await axios.get(`${catalogEndpoint}?perPage=1000000`) // Fetch all products from catalog
    const {
      data: { data: products },
    } = await axios.get(`${apiEndpoint}?limit=1000000`) // Fetch all products from API

    for (const product of products) {
      if (existingIds.has(product.id)) {
        console.log(`Skipping product ${product.id}, details already exist.`)
        continue
      }

      const catalogData = catalog.find((x: { id: string }) => x.id === product.id)
      const productName = catalogData?.name
      const categoryName = catalogData?.category?.name

      try {
        const generatedDetails = await generateAIDetails(
          categoryName,
          (catalogData?.price || 0) / 100,
          productName,
          product.description,
        )

        console.log(`Generated AI detail for product: ${product.id}`)

        // Save details to the CSV file
        if (generatedDetails) {
          appendToCSV(product.id, generatedDetails)
        }
      } catch (error) {
        console.error(`Error generating AI detail for product ${product.id}:`, error)
      }
    }
  } catch (error) {
    console.error('Error generating AI details file:', error)
  }
}

// Run the main function
main()
