import os
import requests
import json
import csv

api_endpoint = "https://averyapi.com/v2/products"
filepath = os.path.dirname(__file__) + "/product_ai_details.csv"


def update_description(id: str, details: str):
    try:
        # Parse the details JSON string
        details_json = json.loads(details)

        # Extract fields from the JSON
        ai_description = details_json.get("aiDescription", "")
        pros = details_json.get("pros", [])
        cons = details_json.get("cons", [])

        # Fetch the current product data
        endpoint_url = f"{api_endpoint}/{id}"
        res = requests.get(endpoint_url)

        if res.status_code != 200:
            print(f"Failed to fetch product {id}: {res.status_code}")
            return

        # Update the data with AI details
        data = res.json()
        data = data["data"][0]
        data["aiDescription"] = ai_description
        data["pros"] = pros
        data["cons"] = cons

        # Send the updated data to the API
        headers = {"Content-Type": "application/json"}
        payload = json.dumps(data)

        response = requests.put(endpoint_url, data=payload, headers=headers)
        if response.status_code == 200:
            print(f"Product {id} AI details updated successfully")
        else:
            print(f"Failed to update product {id}: {response.status_code}, {response.text}")
    except Exception as e:
        print(f"Error updating product {id}: {str(e)}")


def main():
    try:
        # Read CSV to check product IDs to update
        with open(filepath, newline="", encoding="utf-8") as csvfile:
            csvreader = csv.reader(csvfile)
            # Skip header
            next(csvreader, None)

            # Loop through every product ID and update the details
            for row in csvreader:
                id, details = row
                update_description(id.strip(), details.strip())

        print("All product AI details updated")
    except Exception as e:
        print(f"Error processing CSV file: {str(e)}")


if __name__ == "__main__":
    main()
