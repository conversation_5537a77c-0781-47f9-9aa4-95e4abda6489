import requests

api_endpoint = "https://averyapi.com/projects"
headers = {"Content-Type": "application/json"}


def update_project(endpoint, project_data):
    requests.put(endpoint, json=project_data, headers=headers)
    print(f"Product {endpoint} description updated")


def main():
    res = requests.get(api_endpoint)
    projects = res.json()

    for project in projects:
        endpoint = f'{api_endpoint}/{project["id"]}'
        project_res = requests.get(endpoint)
        project_data = project_res.json()
        update_project(endpoint, project_data)

    print("All product descriptions updated")


if __name__ == "__main__":
    main()
