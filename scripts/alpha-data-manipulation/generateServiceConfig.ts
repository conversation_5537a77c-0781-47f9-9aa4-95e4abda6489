'use strict'
import * as fs from 'fs'

// Define the paths for mirror and vanity data
const vanityDataPath = `../../src/main/resources/data/vanityData.json`
const mirrorDataPath = `../../src/main/resources/data/mirrorData.json`

const vanityData = JSON.parse(fs.readFileSync(vanityDataPath, 'utf-8'))
const mirrorData = JSON.parse(fs.readFileSync(mirrorDataPath, 'utf-8'))

// Extract the minimum length for a given product type
const getMinLengthForProductType = (productArray: { length: string }[]) => {
  const lengths = productArray.map((product) => parseFloat(product.length)).filter((length) => !isNaN(length)) // Filter out any NaN values just in case
  return Math.min(...lengths)
}

const serviceConfig = {
  products: {
    vanity: {
      minSize: getMinLengthForProductType(vanityData),
    },
    mirror: {
      minSize: getMinLengthForProductType(mirrorData),
    },
  },
}

fs.writeFileSync('../../src/main/resources/serviceConfig.json', JSON.stringify(serviceConfig, null, 2))
