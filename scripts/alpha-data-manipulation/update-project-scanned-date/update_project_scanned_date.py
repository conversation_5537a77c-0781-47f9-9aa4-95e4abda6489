import os
import requests
import json
import csv

from datetime import datetime, timezone
from dateutil import parser

api_endpoint = "https://averyapi.com/projects"
filepath = os.path.dirname(__file__) + "/project_dates.csv"
resultpath = os.path.dirname(__file__) + "/result.sql"
rollbackpath = os.path.dirname(__file__) + "/rollback.sql"


def convert_to_apple_epoch(date_str):
    apple_epoch_start = datetime(2001, 1, 1, tzinfo=timezone.utc)

    try:
        date_obj = parser.parse(date_str)
        if date_obj.tzinfo is None:
            date_obj = date_obj.replace(tzinfo=timezone.utc)
    except ValueError:
        raise ValueError(f"Date format for '{date_str}' is not recognized.")

    apple_epoch_time = (date_obj - apple_epoch_start).total_seconds()
    return apple_epoch_time


def main():
    previous = []
    result = []
    with open(filepath, newline="", encoding="utf-8") as csvfile:
        csvreader = csv.reader(csvfile)
        # Skip header
        next(csvreader, None)
        for row in csvreader:
            id, date = row
            apple_epoch = convert_to_apple_epoch(date)
            res = requests.get(f"{api_endpoint}/{id}")
            project = res.json()
            scan: dict = project["scan"]

            previous.append([id, scan.copy()])
            scan["scannedDate"] = apple_epoch
            result.append([id, scan])

    if os.path.exists(resultpath):
        os.remove(resultpath)

    with open(rollbackpath, mode="w+") as sqlfile:
        sqlfile.write("-- psql -U postgres -d mydatabase -h localhost -p 5432 -f rollback.sql\n\n")
        sqlfile.write("BEGIN;\n\n")
        for row in previous:
            id, scan = row
            sqlfile.write(
                f"\tUPDATE projects SET scan='{json.dumps(scan, separators=(',', ':'))}' WHERE id = '{id}';\n"
            )
        sqlfile.write("COMMIT;\n")

    with open(resultpath, mode="w+") as sqlfile:
        sqlfile.write("-- psql -U postgres -d mydatabase -h localhost -p 5432 -f result.sql\n\n")
        sqlfile.write("BEGIN;\n\n")
        for row in result:
            id, scan = row
            sqlfile.write(
                f"\tUPDATE projects SET scan='{json.dumps(scan, separators=(',', ':'))}' WHERE id = '{id}';\n"
            )
        sqlfile.write("COMMIT;\n")

    print("All project dates updated")


if __name__ == "__main__":
    main()
