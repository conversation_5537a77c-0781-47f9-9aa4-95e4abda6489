import os
import requests
import csv


api_endpoint = "https://averyapi.com/projects"
filepath = os.path.dirname(__file__) + "/projects.csv"


def main():
    with open(filepath, newline="", encoding="utf-8") as csvfile:
        csvreader = csv.reader(csvfile)
        # Skip header
        next(csvre<PERSON>, None)
        for row in csvreader:
            is_test, project_id = row
            endpoint = f"{api_endpoint}/{project_id}"

            # res = requests.get(endpoint)
            # project = res.json()
            # project["isReal"] = is_test != "Yes"
            is_real = is_test != "Yes"
            if not is_real:
                requests.put(endpoint, json={"isReal": is_real})
                print(f"Successfully updated {project_id}!")

    print("All project dates updated")


if __name__ == "__main__":
    main()
