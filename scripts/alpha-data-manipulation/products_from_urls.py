import argparse
import csv
import json
import os

from openai import OpenAI
import requests

PERPLEXITY_API_KEY = os.environ.get("PERPLEXITY_API_KEY")


def extract_product_data(llm_client, url):
    """Extracts product data from a URL using the Perplexity API."""

    prompt = f"""Extract product information,
including highlights, description, specifications (grouped by category),
total reviews, and average rating from this URL and any reputable sources online: {url}

Omit any fields for which data is not available.
Do not include an explicit preamble stating that this is the info I asked for
because I already know that."""

    try:
        response = llm_client.chat.completions.create(
            model="sonar-pro",
            messages=[{"role": "user", "content": prompt}],
            response_format={
                "type": "json_schema",
                "json_schema": {
                    "schema": {
                        "type": "object",
                        "properties": {
                            "id": {
                                "type": "string",
                                "format": "uuid",
                                "description": "Unique identifier for the product",
                            },
                            "highlights": {
                                "type": "array",
                                "items": {"type": "string"},
                                "description": "Array of product highlights",
                            },
                            "description": {
                                "type": "string",
                                "description": "Detailed product description",
                            },
                            "specificationGroup": {
                                "type": "array",
                                "items": {
                                    "type": "object",
                                    "properties": {
                                        "specTitle": {
                                            "type": "string",
                                            "description": "Title of the specification group",
                                        },
                                        "specifications": {
                                            "type": "array",
                                            "items": {
                                                "type": "object",
                                                "properties": {
                                                    "specName": {
                                                        "type": "string",
                                                        "description": "Name of the specification",
                                                    },
                                                    "specValue": {
                                                        "type": "string",
                                                        "description": "Value of the specification",
                                                    },
                                                },
                                                "required": [
                                                    "specName",
                                                    "specValue",
                                                ],
                                            },
                                            "description": "Array of specifications within the group",
                                        },
                                    },
                                    "required": ["specTitle", "specifications"],
                                },
                                "description": "Array of specification groups",
                            },
                            "totalReviews": {
                                "type": "string",
                                "description": "Total number of reviews",
                            },
                            "averageRating": {
                                "type": "string",
                                "description": "Average rating of the product",
                            },
                        },
                        "required": [
                            "id",
                            "highlights",
                            "description",
                            "specificationGroup",
                            "totalReviews",
                            "averageRating",
                        ],
                    },
                },
            },
        )
        return response.choices[0].message.content
    except requests.exceptions.RequestException as e:
        print(f"Error fetching data from Perplexity API for {url}: {e}")
        return None


def parse_product_info(text):
    """Parses the extracted text into the desired JSON structure."""
    try:
        # Attempt to parse the generated text as JSON.  If Perplexity returns valid JSON, this is ideal.
        product_data = json.loads(text)
        return product_data  # If it's already structured, return it.
    except json.JSONDecodeError:
        pass  # If not valid JSON, we'll have to do some more parsing.

    product_data = {
        "id": None,  # Need to populate this from CSV
        "highlights": [],
        "description": None,
        "specificationGroup": [],
        "totalReviews": None,
        "averageRating": None,
    }

    lines = text.splitlines()
    section = None
    for line in lines:
        line = line.strip()
        if not line:
            continue

        if line.startswith("Highlights:"):
            section = "highlights"
        elif line.startswith("Description:"):
            section = "description"
        elif line.startswith("Specifications:"):
            section = "specifications"
        elif line.startswith("Total Reviews:"):
            try:
                product_data["totalReviews"] = int(line.split(":")[1].strip())
            except ValueError:
                pass
        elif line.startswith("Average Rating:"):
            try:
                product_data["averageRating"] = float(line.split(":")[1].strip())
            except ValueError:
                pass
        elif line.endswith(":"):  # Likely a specification title
            section = "spec_group"
            spec_title = line[:-1].strip()
            product_data["specificationGroup"].append(
                {"specTitle": spec_title, "specifications": []}
            )
        elif section == "highlights":
            product_data["highlights"].append(line)
        elif section == "description":
            product_data["description"] = (
                line
                if product_data["description"] is None
                else product_data["description"] + "\n" + line
            )  # Append to make sure we get the full description if it's multiple lines
        elif section == "spec_group" and ":" in line:
            try:
                spec_name, spec_value = [s.strip() for s in line.split(":", 1)]
                product_data["specificationGroup"][-1]["specifications"].append(
                    {"specName": spec_name, "specValue": spec_value}
                )
            except ValueError:  # Handle cases where the split might fail
                pass

    return product_data


def process_csv(csv_filepath, llm_client, output_filepath):
    """Processes the CSV file, extracts product data, and writes it to a JSON file."""

    product_data_list = []
    with open(csv_filepath, "r", encoding="utf-8") as csvfile:
        reader = csv.DictReader(csvfile)
        for row in reader:
            product_id = row["UUID"]
            url = row["URL"]
            print(f"Processing {url}...")
            extracted_text = extract_product_data(llm_client, url)
            if extracted_text:
                product_data = parse_product_info(extracted_text)
                if product_data:
                    product_data["id"] = product_id
                    product_data_list.append(product_data)
                    print(f"Data extracted for {url}")
                else:
                    print(f"Failed to parse data for {url}")
            else:
                print(f"No data extracted for {url}")

    with open(output_filepath, "w", encoding="utf-8") as outfile:
        json.dump(product_data_list, outfile, indent=4, ensure_ascii=False)
    print(f"Product data written to {output_filepath}")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Extract product data from URLs in a CSV file."
    )
    parser.add_argument(
        "csv_file",
        nargs="?",
        help="Path to the CSV file (optional, can be passed with -i)",
    )
    parser.add_argument(
        "-i", "--input", dest="csv_file_flag", help="Path to the CSV file"
    )
    parser.add_argument(
        "-o",
        "--output",
        dest="output_file",
        default="productsToCreate.json",
        help="Path to the output JSON file (default: productsToCreate.json)",
    )
    args = parser.parse_args()

    client = OpenAI(
        api_key=PERPLEXITY_API_KEY,
        base_url="https://api.perplexity.ai",
    )

    csv_file = args.csv_file or args.csv_file_flag or "product_urls.csv"
    output_file = args.output_file

    process_csv(csv_file, client, output_file)
