import os
import requests
import json
import csv

api_endpoint = "https://api.averyapi.com/v2/materials/products/renderable-products/lightings"
filepath = os.path.dirname(__file__) + "/lighting_model_ids.csv"


def update_lighting(row):
    id, modelid, above_supported, side_supported, above_rotation, side_rotation = row
    endpoint_url = f"{api_endpoint}/{id}"

    # Get the current lighting data
    res = requests.get(endpoint_url)
    data = res.json()["data"][0]
    headers = {"Content-Type": "application/json"}

    # Remove unnecessary fields for PUT
    if "id" in data:
        del data["id"]
    if "isKit" in data:
        del data["isKit"]
    if "category" in data:
        del data["category"]
    if "images" in data:
        del data["images"]
    if "productFamilyId" in data:
        del data["productFamilyId"]
    if "variants" in data:
        del data["variants"]
    if "link" in data:
        del data["link"]

    if "featuredImage" in data and data["featuredImage"]:
        data["featuredImageId"] = data["featuredImage"]["id"]
        del data["featuredImage"]
    if "preferredVendor" in data and data["preferredVendor"]:
        data["preferredVendorId"] = data["preferredVendor"]["id"]
        del data["preferredVendor"]

    if "length" in data:
        data["length"] = float(data["length"])
    if "width" in data:
        data["width"] = float(data["width"])
    if "height" in data:
        data["height"] = float(data["height"])
    if "sqftCoverage" in data:
        data["sqftCoverage"] = float(data["sqftCoverage"])

    # Reset any existing model id
    data["sidePlacementId"] = None
    data["abovePlacementId"] = None

    # Assign supported model id
    if above_supported == "TRUE":
        data["abovePlacementId"] = modelid

    if side_supported == "TRUE":
        data["sidePlacementId"] = modelid

    payload = json.dumps(data)
    requests.put(endpoint_url, data=payload, headers=headers)
    print(f"Product {id} default model ids updated")


def main():
    # Read CSV to check product IDs to update
    with open(filepath, newline="", encoding="utf-8") as csvfile:
        csvreader = csv.reader(csvfile)
        # Skip header
        next(csvreader, None)
        # Loop through every product ID and update the description
        for row in csvreader:
            update_lighting(row)

    print("All lightings updated")


if __name__ == "__main__":
    main()
