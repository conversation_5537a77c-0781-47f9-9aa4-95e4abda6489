import fs from 'fs'
import axios from 'axios'
import path from 'path'
import { defaultFilePath } from '../materials-service-scraper/productConfiguration'

interface Product {
  id: string
  [key: string]: any
}

interface ProductData {
  data: Product[]
  [key: string]: any
}

const dataDir = path.join(__dirname, defaultFilePath)

async function fetchValuesFromAPI(apiEndpoint: string): Promise<ProductData> {
  try {
    const response = await axios.get<ProductData>(apiEndpoint)
    return response.data
  } catch (error) {
    console.error('Error fetching Values from the API:', error)
    throw error
  }
}

async function readValuesFromFile(fileName: string): Promise<Product[]> {
  try {
    const filePath = path.join(dataDir, fileName)
    const data = JSON.parse(fs.readFileSync(filePath, 'utf8'))
    return data
  } catch (error) {
    console.error('Error reading Values from the file:', error)
    throw error
  }
}

function writeValuesToFile(Values: Product[], fileName: string): void {
  try {
    const data = JSON.stringify(Values, null, 2)
    const filePath = path.join(dataDir, fileName)
    fs.writeFileSync(filePath, data)
    console.log('File updated successfully.')
  } catch (error) {
    console.error('Error writing Values to the file:', error)
    throw error
  }
}

async function updateValues(fileName: string, apiEndpoint: string): Promise<void> {
  try {
    const apiValues = await fetchValuesFromAPI(apiEndpoint)
    const fileValues = await readValuesFromFile(fileName)
    const valuesToArray = Object.values(fileValues)
    const updatedValues = valuesToArray.filter((product) =>
      apiValues.data.some((apiProduct) => apiProduct.id === product.id),
    )
    writeValuesToFile(updatedValues, fileName)
  } catch (error) {
    console.error('Error updating Values:', error)
  }
}

function updateResource() {
  console.log('Updating Resource...')
  const fileName = 'productDetails.json'
  const apiEndpoint = 'https://api.averyapi.com/v2/materials/products?perPage=5000'

  updateValues(fileName, apiEndpoint)
}

updateResource()
