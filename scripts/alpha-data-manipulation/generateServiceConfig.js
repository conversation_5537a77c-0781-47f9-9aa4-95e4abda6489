'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
var fs = require('fs')
// Define the paths for mirror and vanity data
var vanityDataPath = '../src/main/resources/data/vanityData.json'
var mirrorDataPath = '../src/main/resources/data/mirrorData.json'
var vanityData = JSON.parse(fs.readFileSync(vanityDataPath, 'utf-8'))
var mirrorData = JSON.parse(fs.readFileSync(mirrorDataPath, 'utf-8'))
// Extract the minimum length for a given product type
var getMinLengthForProductType = function (productArray) {
  var lengths = productArray
    .map(function (product) {
      return parseFloat(product.length)
    })
    .filter(function (length) {
      return !isNaN(length)
    }) // Filter out any NaN values just in case
  return Math.min.apply(Math, lengths)
}
var serviceConfig = {
  products: {
    vanity: {
      minSize: getMinLengthForProductType(vanityData),
    },
    mirror: {
      minSize: getMinLengthForProductType(mirrorData),
    },
  },
}
fs.writeFileSync('../src/main/resources/serviceConfig.json', JSON.stringify(serviceConfig, null, 2))
