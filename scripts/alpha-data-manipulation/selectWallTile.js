var fs = require('fs')
var path = require('path')
// Load the design templates and product data
var designTemplatesPath = path.join(__dirname, '../src/main/resources/data/designTemplates.json')
var productDataPath = path.join(__dirname, '../src/main/resources/data/productData.json')
var designTemplates = require(designTemplatesPath)
var productData = require(productDataPath)
// Check if a tile ID exists within the wallTile category
var findTileInWallTileData = function (tileId) {
  return !!productData['wallTile'].find(function (tile) {
    return tile.id === tileId
  })
}
// Iterate through each design template
designTemplates.forEach(function (template) {
  if (!template.wallTile) {
    if (template.floorTile && findTileInWallTileData(template.floorTile)) {
      template.wallTile = template.floorTile
    } else if (template.showerWallTile && findTileInWallTileData(template.showerWallTile)) {
      template.wallTile = template.showerWallTile
    } else {
      // If no other condition matches, assign a random wallTile from productData
      var randomWallTile = productData['wallTile'][Math.floor(Math.random() * productData['wallTile'].length)]
      template.wallTile = randomWallTile.id
    }
  }
})
// Write the modified design templates back to the file
fs.writeFileSync(designTemplatesPath, JSON.stringify(designTemplates, null, 2))
console.log('Updated designTemplates.json with wall tiles.')
