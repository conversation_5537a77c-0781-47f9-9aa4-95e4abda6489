import os
import requests
import json
import csv

api_endpoint = "https://averyapi.com/products"
filepath = os.path.dirname(__file__) + "/product_descriptions.csv"


def update_description(id: str, description: str):
    endpoint_url = f"{api_endpoint}/{id}"
    res = requests.get(endpoint_url)

    data = res.json()
    data["description"] = description

    headers = {"Content-Type": "application/json"}
    payload = json.dumps(data)

    requests.put(endpoint_url, data=payload, headers=headers)
    print(f"Product {id} description updated")


def main():
    # Read CSV to check product IDs to update
    with open(filepath, newline="", encoding="utf-8") as csvfile:
        csvreader = csv.reader(csvfile)
        # Skip header
        next(csvreader, None)
        # Loop through every product ID and update the description
        for row in csvreader:
            id, description = row
            update_description(id, description)

    print("All product descriptions updated")


if __name__ == "__main__":
    main()
