const axios = require('axios')

const AVERAGE_BASE_PROJECTS = 'https://averyapi.com/v2/projects'

async function getAllProjectsWithMaterials() {
  try {
    const { data: response } = await axios.get(AVERAGE_BASE_PROJECTS)
    return response.data ? response.data.filter((project) => project.materials !== null) : []
  } catch (error) {
    console.error(`Error fetching projects:`, error)
    return []
  }
}

async function fetchAvailableFrom(materialId) {
  try {
    const response = await axios.get(
      `https://api.averyapi.com/v2/materials/products/${materialId}?include[]=vendor_data`,
    )
    return response.data.data[0].availableFrom || []
  } catch (error) {
    console.error(`Error fetching availableFrom for material ID ${materialId}:`, error)
    return []
  }
}

async function updateProject(projectId, body) {
  try {
    const response = await axios.put(`${AVERAGE_BASE_PROJECTS}/${projectId}`, {
      ...body,
    })
    return response
  } catch (error) {
    console.error(`Error updating project ${projectId}:`, error)
    return []
  }
}

async function getUpdatedMaterialsList(materialsList) {
  return await Promise.all(
    materialsList.map(async (material) => {
      if (!material?.availableFrom?.length && material.id) {
        material.availableFrom = await fetchAvailableFrom(material.id)
      }
      return material
    }),
  )
}

async function backFillAvailableFromOnMaterials() {
  try {
    const projects = await getAllProjectsWithMaterials()
    console.warn(`Total projects: ${projects.length}`)

    const batchSize = 10
    for (let i = 0; i < projects.length; i += batchSize) {
      const batch = projects.slice(i, i + batchSize)
      await Promise.all(
        batch.map(async (project) => {
          const updatedMaterialsList = await getUpdatedMaterialsList(project.materials || [])
          await updateProject(project.id, { materials: updatedMaterialsList })
          console.warn(`${project.id} UPDATED`)
        }),
      )
    }

    console.log('Backfill complete')
    return projects
  } catch (error) {
    console.error('Backfill error:', error)
    throw new Error('Backfill error')
  }
}

backFillAvailableFromOnMaterials()
