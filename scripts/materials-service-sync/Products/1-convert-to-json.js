const fs = require('fs')
const csv = require('csv-parser')
const path = require('path')
const util = require('util')

const readDir = util.promisify(fs.readdir)

const convertCsvToJson = async (directory) => {
  let files = await readDir(directory)
  for (let file of files) {
    let filePath = path.join(directory, file)
    let stats = fs.statSync(filePath)
    if (stats.isFile() && path.extname(file) === '.csv') {
      const data = []
      fs.createReadStream(filePath)
        .pipe(csv())
        .on('data', (row) => data.push(row))
        .on('end', () => {
          const jsonPath = filePath.replace('.csv', '.json')
          fs.writeFileSync(jsonPath, JSON.stringify(data, null, 2))
          console.log(`CSV file ${filePath} converted to JSON.`)
        })
    } else if (stats.isDirectory()) {
      convertCsvTo<PERSON>son(filePath)
    }
  }
}

convertCsvToJson('./product-data')
