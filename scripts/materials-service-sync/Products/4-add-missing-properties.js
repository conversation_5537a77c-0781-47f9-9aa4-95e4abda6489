const fs = require('fs')
const path = require('path')
const util = require('util')

const readDir = util.promisify(fs.readdir)

const enrichObjectsInJson = async (directory) => {
  let files = await readDir(directory)
  for (let file of files) {
    let filePath = path.join(directory, file)
    let stats = fs.statSync(filePath)
    if (stats.isFile() && path.extname(file) === '.json') {
      let rawData = fs.readFileSync(filePath)
      let jsonData = JSON.parse(rawData)

      if (!Array.isArray(jsonData)) {
        console.error(`Error: Expected array in ${filePath}, found ${typeof jsonData}`)
        continue
      }

      jsonData = jsonData.map((obj) => {
        obj.description = obj.description || 'TODO'
        obj.isGCSupplied = false
        obj.createNewProductFamily = true
        if (directory.includes('renderable-products')) {
          obj.displayName = obj.name
        }
        return obj
      })

      fs.writeFileSync(filePath, JSON.stringify(jsonData, null, 2))
      console.log(`Enriched objects in ${filePath}.`)
    } else if (stats.isDirectory()) {
      enrichObjectsInJson(filePath)
    }
  }
}

enrichObjectsInJson('./product-data')
