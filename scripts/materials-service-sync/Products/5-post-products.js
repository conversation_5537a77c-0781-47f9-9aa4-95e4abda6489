const fs = require('fs')
const path = require('path')
const util = require('util')
const axios = require('axios')
const readDir = util.promisify(fs.readdir)

const postProductsToApi = async (directory) => {
  let files = await readDir(directory)
  for (let file of files) {
    let filePath = path.join(directory, file)
    let stats = fs.statSync(filePath)
    if (stats.isFile() && path.extname(file) === '.json') {
      let rawData = fs.readFileSync(filePath)
      let jsonData = JSON.parse(rawData)

      if (!Array.isArray(jsonData)) {
        console.error(`Error: Expected array in ${filePath}, found ${typeof jsonData}`)
        continue
      }

      const productCategory = path.basename(file, '.json')
      // Use for products of defined types
      // let url = `https://api.averyapi.com/v2/materials/products/${productCategory}`;
      // Use for generic products
      let url = `https://api.averyapi.com/catalog/v2/products`

      if (directory.includes('renderable-products')) {
        url += `/renderable-products/${productCategory}`
      }

      for (let product of jsonData) {
        const getProductUrl = `https://api.averyapi.com/catalog/v2/products/${product.id}`
        try {
          // Check if product already exists
          let getResponse = await axios.get(getProductUrl)

          // If the product does not exist (based on the empty data array), attempt to post the product
          if (getResponse.data.data.length === 0) {
            await axios.post(url, product)
          }
        } catch (error) {
          console.error(`Error posting product ${product.id} to ${url}: ${error}`)
        }
      }

      console.log(`Posted products from ${filePath} to ${url}.`)
    } else if (stats.isDirectory()) {
      postProductsToApi(filePath)
    }
  }
}

postProductsToApi('./product-data')
