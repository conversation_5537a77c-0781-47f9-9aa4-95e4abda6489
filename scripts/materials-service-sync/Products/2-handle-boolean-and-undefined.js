const fs = require('fs')
const path = require('path')
const util = require('util')

const readDir = util.promisify(fs.readdir)

const convertValuesInJson = async (directory) => {
  let files = await readDir(directory)
  for (let file of files) {
    let filePath = path.join(directory, file)
    let stats = fs.statSync(filePath)
    if (stats.isFile() && path.extname(file) === '.json') {
      let rawData = fs.readFileSync(filePath)
      let jsonData = JSON.parse(rawData)

      if (!Array.isArray(jsonData)) {
        console.error(`Error: Expected array in ${filePath}, found ${typeof jsonData}`)
        continue
      }

      jsonData = jsonData.map((obj) => {
        for (let key in obj) {
          if (obj[key] === 'undefined') {
            obj[key] = undefined
          }
          if (obj[key] === 'empty') {
            obj[key] = ''
          }
          if (obj[key] === 'null') {
            obj[key] = null
          }
          if (obj[key] === 'TRUE') {
            obj[key] = true
          }
          if (obj[key] === 'FALSE') {
            obj[key] = false
          }
          if (obj[key] === '') {
            obj[key] = null
          }
        }
        return obj
      })

      fs.writeFileSync(filePath, JSON.stringify(jsonData, null, 2))
      console.log(`Converted values in ${filePath}.`)
    } else if (stats.isDirectory()) {
      convertValuesInJson(filePath)
    }
  }
}

convertValuesInJson('./product-data')
