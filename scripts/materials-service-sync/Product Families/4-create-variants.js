const fs = require('fs')
const path = require('path')

const directoryPath = './Families'

// Function to modify JSON data
function modifyJsonData(file) {
  const jsonFilePath = path.join(directoryPath, file)

  // Read JSON data from file
  let data = fs.readFileSync(jsonFilePath)
  let productFamilies = JSON.parse(data)

  // Loop through each product family
  productFamilies.forEach((productFamily) => {
    // Loop through each product in the product family
    productFamily.products.forEach((product) => {
      let variants = {}

      // Loop through each property of the product
      Object.keys(product).forEach((key) => {
        if (key !== 'productId') {
          // Move the property into the 'variants' object
          variants[key] = product[key]
          delete product[key]
        }
      })

      // Add variants object to product
      product.variants = variants
    })
  })

  // Write modified data back to file
  fs.writeFile(jsonFilePath, JSON.stringify(productFamilies, null, 2), (err) => {
    if (err) throw err
    console.log(`Modified data in ${file}.`)
  })
}

// Function to read directory
fs.readdir(directoryPath, function (err, files) {
  if (err) {
    return console.log('Unable to scan directory: ' + err)
  }

  // Listing all json files using forEach
  files.filter((file) => path.extname(file).toLowerCase() === '.json').forEach(modifyJsonData)
})
