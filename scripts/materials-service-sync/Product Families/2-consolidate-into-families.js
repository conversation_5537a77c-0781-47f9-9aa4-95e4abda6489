const fs = require('fs')
const path = require('path')

const directoryPath = './Families'

// Function to consolidate JSON data
function consolidateJsonData(file) {
  const jsonFilePath = path.join(directoryPath, file)

  // Read JSON data from file
  let data = fs.readFileSync(jsonFilePath)
  let products = JSON.parse(data)

  // Create a map to store consolidated data
  let productFamilyMap = new Map()

  // Loop through each product
  products.forEach((product) => {
    // Check if product family already exists in map
    if (productFamilyMap.has(product.productFamilyId)) {
      // Get the existing product family
      let existingProductFamily = productFamilyMap.get(product.productFamilyId)

      // Add the product to the existing product family
      let newProduct = { ...product }
      delete newProduct.category
      delete newProduct.productFamilyId
      delete newProduct.productFamilyName
      existingProductFamily.products.push(newProduct)
    } else {
      // Create a new product family
      let productFamily = {
        category: product.category,
        productFamilyId: product.productFamilyId,
        productFamilyName: product.productFamilyName,
        products: [],
      }

      // Add the product to the new product family
      let newProduct = { ...product }
      delete newProduct.category
      delete newProduct.productFamilyId
      delete newProduct.productFamilyName
      productFamily.products.push(newProduct)

      // Add the new product family to the map
      productFamilyMap.set(product.productFamilyId, productFamily)
    }
  })

  // Write consolidated data back to file
  fs.writeFile(jsonFilePath, JSON.stringify(Array.from(productFamilyMap.values()), null, 2), (err) => {
    if (err) throw err
    console.log(`Consolidated data in ${file}.`)
  })
}

// Function to read directory
fs.readdir(directoryPath, function (err, files) {
  if (err) {
    return console.log('Unable to scan directory: ' + err)
  }

  // Listing all json files using forEach
  files.filter((file) => path.extname(file).toLowerCase() === '.json').forEach(consolidateJsonData)
})
