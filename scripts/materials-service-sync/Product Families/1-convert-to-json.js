const fs = require('fs')
const path = require('path')
const csvToJson = require('csvtojson')

const directoryPath = './Families'

// Function to convert CSV to JSON
function convertCsvToJson(file) {
  const csvFilePath = path.join(directoryPath, file)
  const jsonFilePath = path.join(directoryPath, `${path.basename(file, '.csv')}.json`)

  csvToJson()
    .fromFile(csvFilePath)
    .then((jsonObj) => {
      fs.writeFile(jsonFilePath, JSON.stringify(jsonObj, null, 2), (err) => {
        if (err) throw err
        console.log(`Converted ${file} to JSON.`)
      })
    })
}

// Function to read directory
fs.readdir(directoryPath, function (err, files) {
  if (err) {
    return console.log('Unable to scan directory: ' + err)
  }

  // listing all csv files using forEach
  files.filter((file) => path.extname(file).toLowerCase() === '.csv').forEach(convertCsvToJson)
})
