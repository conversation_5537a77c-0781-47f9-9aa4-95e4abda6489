const fs = require('fs')
const path = require('path')
const axios = require('axios')

const directoryPath = './Families' // replace with your directory

let successCount = 0
let failureCount = 0

// Function to check product family existence
async function checkProductFamilyExistence(productFamily) {
  try {
    const response = await axios.get(`https://api.madexthd.com/v2/materials/product-families/${productFamily.id}`)

    // If the data array in the response is not empty, the product family exists
    if (response.data.data.length > 0) {
      successCount++
    } else {
      failureCount++
    }
  } catch (error) {
    console.error(`Failed to fetch product family: ${productFamily.id}`)
    failureCount++
  }
}

// Function to read and check JSON data
async function checkJsonData(file) {
  const jsonFilePath = path.join(directoryPath, file)

  // Read JSON data from file
  let data = fs.readFileSync(jsonFilePath)
  let productFamilies = JSON.parse(data)

  // Use Promise.all to wait for all checks to complete
  await Promise.all(productFamilies.map(checkProductFamilyExistence))

  console.log(`Success count: ${successCount}`)
  console.log(`Failure count: ${failureCount}`)
}

// Function to read directory
fs.readdir(directoryPath, function (err, files) {
  if (err) {
    return console.log('Unable to scan directory: ' + err)
  }

  // Listing all json files using forEach
  files.filter((file) => path.extname(file).toLowerCase() === '.json').forEach(checkJsonData)
})
