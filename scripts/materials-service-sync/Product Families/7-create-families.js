const fs = require('fs')
const path = require('path')
const axios = require('axios')

const directoryPath = './Families'

// Function to create product family
async function createProductFamily(productFamily) {
  try {
    const response = await axios.post('https://api.madexthd.com/v2/materials/product-families/', productFamily)

    if (response.status === 200 || response.status === 201) {
      console.log(`Product family ${productFamily.id} created successfully`)
    }
  } catch (error) {
    console.error(`Failed to create product family: ${productFamily.id}`, error.response.data)
  }
}

// Function to read and create JSON data
async function createJsonData(file) {
  const jsonFilePath = path.join(directoryPath, file)

  // Read JSON data from file
  let data = fs.readFileSync(jsonFilePath)
  let productFamilies = JSON.parse(data)

  // Use Promise.all to wait for all product family creations to complete
  await Promise.all(productFamilies.map(createProductFamily))
}

// Function to read directory
fs.readdir(directoryPath, function (err, files) {
  if (err) {
    return console.log('Unable to scan directory: ' + err)
  }

  // Listing all json files using forEach
  files.filter((file) => path.extname(file).toLowerCase() === '.json').forEach(createJsonData)
})
