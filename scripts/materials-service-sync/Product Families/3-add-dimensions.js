const fs = require('fs')
const path = require('path')

const directoryPath = './Families' // replace with your directory

// Function to enrich JSON data
function enrichJsonData(file) {
  const jsonFilePath = path.join(directoryPath, file)

  // Read JSON data from file
  let data = fs.readFileSync(jsonFilePath)
  let productFamilies = JSON.parse(data)

  // Loop through each product family
  productFamilies.forEach((productFamily) => {
    let dimensions = new Set()

    // Loop through each product in the product family
    productFamily.products.forEach((product) => {
      // Add the unique dimensions to the set, excluding 'productId'
      Object.keys(product).forEach((key) => {
        if (key !== 'productId') dimensions.add(key)
      })
    })

    // Add dimensions array to product family
    productFamily.dimensions = Array.from(dimensions)
  })

  // Write enriched data back to file
  fs.writeFile(jsonFilePath, JSON.stringify(productFamilies, null, 2), (err) => {
    if (err) throw err
    console.log(`Enriched data in ${file}.`)
  })
}

// Function to read directory
fs.readdir(directoryPath, function (err, files) {
  if (err) {
    return console.log('Unable to scan directory: ' + err)
  }

  // Listing all json files using forEach
  files.filter((file) => path.extname(file).toLowerCase() === '.json').forEach(enrichJsonData)
})
