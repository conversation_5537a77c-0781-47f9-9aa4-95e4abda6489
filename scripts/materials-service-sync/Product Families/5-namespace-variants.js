const fs = require('fs')
const path = require('path')

const directoryPath = './Families'

// Function to modify JSON data
function modifyJsonData(file) {
  const jsonFilePath = path.join(directoryPath, file)

  // Read JSON data from file
  let data = fs.readFileSync(jsonFilePath)
  let productFamilies = JSON.parse(data)

  // Loop through each product family
  productFamilies.forEach((productFamily) => {
    // Loop through each product in the product family
    productFamily.products.forEach((product) => {
      // Loop through each variant in the product
      Object.keys(product.variants).forEach((variant) => {
        // Modify variant to be an object with a 'name' property
        product.variants[variant] = { name: product.variants[variant] }
      })
    })
  })

  // Write modified data back to file
  fs.writeFile(jsonFilePath, JSON.stringify(productFamilies, null, 2), (err) => {
    if (err) throw err
    console.log(`Modified data in ${file}.`)
  })
}

// Function to read directory
fs.readdir(directoryPath, function (err, files) {
  if (err) {
    return console.log('Unable to scan directory: ' + err)
  }

  // Listing all json files using forEach
  files.filter((file) => path.extname(file).toLowerCase() === '.json').forEach(modifyJsonData)
})
