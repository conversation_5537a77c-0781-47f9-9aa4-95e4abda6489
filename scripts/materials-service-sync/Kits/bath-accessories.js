const fs = require('fs')
const csv = require('csv-parser')
const axios = require('axios')

// Function to make PUT request
const updateProduct = async (productId, componentProductId) => {
  try {
    const response = await axios.put(
      `https://api.madexthd.com/v2/materials/products/${productId}/component-products/${componentProductId}`,
    )
    console.log(`Status: ${response.status}`)
    console.log('Body: ', response.data)
  } catch (error) {
    console.error(`Failed to update product: ${error}`)
  }
}

// Read the CSV file and parse it
fs.createReadStream('kits.csv')
  .pipe(csv({ separator: ',', headers: ['productId', 'componentProductId'] }))
  .on('data', (row) => {
    // For each row in the CSV, make the PUT request
    updateProduct(row.productId, row.componentProductId)
  })
  .on('end', () => {
    console.log('CSV file successfully processed')
  })
