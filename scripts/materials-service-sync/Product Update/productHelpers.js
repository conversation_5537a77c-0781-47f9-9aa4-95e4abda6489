const axios = require('axios')

function transformProductData(product) {
  // Copy the product data and remove unwanted properties
  const newProduct = { ...product }
  delete newProduct.id
  delete newProduct.category
  delete newProduct.images
  delete newProduct.productFamilyId
  delete newProduct.variants

  // If featuredImage is present perserve it otherwise we need to explicitly set it to null on new products
  if (product.featuredImage) {
    newProduct.featuredImageId = product.featuredImage.id
  } else {
    newProduct.featuredImageId = null
  }
  delete newProduct.featuredImage

  if (product.additionalPricingInfo) {
    newProduct.msrp = product.additionalPricingInfo.msrp
  }
  delete newProduct.additionalPricingInfo

  // Rename fields and delete original property.
  if (product.preferredVendor) {
    newProduct.preferredVendorId = product.preferredVendor.id
    delete newProduct.preferredVendor
  }

  // Convert length, width, and height from strings to numbers
  if (newProduct.length) {
    newProduct.length = Number(newProduct.length)
  }
  if (newProduct.width) {
    newProduct.width = Number(newProduct.width)
  }
  if (newProduct.height) {
    newProduct.height = Number(newProduct.height)
  }
  if (newProduct.sqftCoverage) {
    newProduct.sqftCoverage = Number(newProduct.sqftCoverage)
  }

  return newProduct
}

async function updateProduct(partialProduct, path) {
  let productId = partialProduct.id
  delete partialProduct.id

  try {
    // Fetch the existing product data
    const getProductResponse = await axios.get(`https://api.averyapi.com/v2/materials/${path}/${productId}?include[]=additional_pricing_info`)

    const product = getProductResponse.data.data[0]

    // Transform the product data into the shape required for the body of the PUT request
    const putProductData = transformProductData(product)

    // Apply the updates to the products
    for (const key in partialProduct) {
      putProductData[key] = Number(partialProduct[key])
    }

    //console.log(JSON.stringify(putProductData));

    // Send a PUT request to update the product data
    const putProductResponse = await axios.put(
      `https://api.averyapi.com/v2/materials/${path}/${productId}`,
      putProductData,
    )
    console.log(`Updated product ${productId}`)
  } catch (error) {
    console.error(`Failed to update product ${productId}: ${error}`)
  }
}

module.exports = { transformProductData, updateProduct }
