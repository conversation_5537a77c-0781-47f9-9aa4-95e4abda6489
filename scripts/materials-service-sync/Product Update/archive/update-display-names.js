const fs = require('fs')
const csv = require('csv-parser')
const { updateProduct } = require('./productHelpers')

// Create a read stream for the CSV file
const readStream = fs.createReadStream('displayNames.csv')

// Pipe the read stream into csv-parser
readStream
  .pipe(csv())
  .on('data', (row) => {
    // Update each product
    updateProduct(row.id, { displayName: row.displayName })
  })
  .on('end', () => {
    console.log('CSV file successfully processed')
  })
