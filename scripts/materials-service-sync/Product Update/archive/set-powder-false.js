const fs = require('fs')
const { updateProduct } = require('./productHelpers') // replace with the actual path to your file

// Read the file
fs.readFile('powder.txt', 'utf8', function (err, data) {
  if (err) {
    return console.log(err)
  }

  // Split the file contents into lines
  const productIds = data.split('\n')

  // Update each product
  for (const productId of productIds) {
    updateProduct(productId, { powderRoom: false })
  }
})
