const fs = require('fs')
const csv = require('csv-parser')
const { updateProduct } = require('./productHelpers')

function sleep(ms) {
  return new Promise((resolve) => setTimeout(resolve, ms))
}

async function processRows(rows) {
  for (const row of rows) {
    const updateObj = {}
    // Build the update object
    Object.keys(row).forEach((key) => {
      updateObj[key] = row[key]
    })

    // If updateProduct returns a promise, await it so we do them one at a time
    await updateProduct(updateObj, 'products/renderable-products/vanities')

    // Sleep for 1 second (1000 ms) before proceeding to next row
    await sleep(1000)
  }
}

function run() {
  const results = []
  fs.createReadStream('updates.csv')
    .pipe(csv())
    .on('data', (row) => {
      results.push(row)
    })
    .on('end', async () => {
      console.log('CSV file successfully read. Starting updates...')

      await processRows(results)

      console.log('All updates completed.')
    })
}

run()