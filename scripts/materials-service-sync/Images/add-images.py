#!/usr/bin/env python3

import requests

# Path to the file containing UUIDs
file_path = "ids.txt"


# Function to read UUIDs from a file
def read_uuids(file_path):
    with open(file_path, "r") as file:
        return [line.strip() for line in file]


# Function to send POST request for each UUID
def send_post_requests(uuids):
    for uuid in uuids:
        url = f"https://api.averyapi.com/catalog/v2/products/{uuid}/images"
        payload = {
            "url": f"https://cdn.arcstudio.ai/images/products/{uuid}/1.jpeg",
            "setAsFeaturedImage": True,
        }
        headers = {"Content-Type": "application/json"}

        response = requests.post(url, json=payload, headers=headers)

        if response.status_code == 201:
            print(f"Success for UUID: {uuid}")
        else:
            print(
                f"Failed for UUID: {uuid}, Status code: {response.status_code}, Response: {response.text}"
            )


# Reading UUIDs from the file
uuids = read_uuids(file_path)

# Sending POST requests for each UUID
send_post_requests(uuids)
