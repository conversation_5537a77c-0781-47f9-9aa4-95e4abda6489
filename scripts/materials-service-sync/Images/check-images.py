#!/usr/bin/env python3

import requests

def fetch_product_uuids():
    with open('ids.txt', 'r') as file:
        uuids = [line.strip() for line in file.readlines()]
    return uuids

def check_image_exists(uuid):
    url = f"https://cdn.arcstudio.ai/images/products/{uuid}/1.jpeg"
    headers = {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3'}
    response = requests.get(url, headers=headers, allow_redirects=True)
    return response.status_code == 200

def main():
    uuids = fetch_product_uuids()
    missing_images = []

    for uuid in uuids:
        if not check_image_exists(uuid):
            missing_images.append(uuid)
            print(f"Missing image for product UUID: {uuid}")

    if not missing_images:
        print("All products have images.")
    else:
        print(f"Total missing images: {len(missing_images)}")

if __name__ == "__main__":
    main()
