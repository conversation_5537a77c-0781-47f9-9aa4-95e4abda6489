const fs = require('fs')
const axios = require('axios')

fs.readFile('singles.csv', 'utf8', (err, data) => {
  if (err) {
    console.error(err)
    return
  }

  let lines = data.split('\n')

  lines.forEach((line) => {
    let parts = line.split(',')

    if (parts.length === 2) {
      let productId = parts[0]
      let associatedProductId = parts[1]

      axios
        .put(`https://api.averyapi.com/v2/materials/products/${productId}/associated-products/${associatedProductId}`)
        .then((response) => {
          console.log(`${associatedProductId} associated with ${productId}`)
        })
        .catch((error) => {
          console.error(`Failed to associate ${associatedProductId} with ${productId}: ${error}`)
        })
    }
  })
})
