import requests
from bs4 import BeautifulSoup
import os
import logging
from requests.adapters import HTTPA<PERSON>pter
from requests.packages.urllib3.util.retry import Retry

# Setup logging
logging.basicConfig(filename='image_downloader.log', level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def setup_session():
    session = requests.Session()
    retries = Retry(total=5, backoff_factor=1, status_forcelist=[ 429, 500, 502, 503, 504 ])
    session.mount('http://', HTTPAdapter(max_retries=retries))
    session.mount('https://', HTTPAdapter(max_retries=retries))
    return session

def download_image(session, id, image_url):
    folder_path = os.path.join('images', id)
    os.makedirs(folder_path, exist_ok=True)
    image_path = os.path.join(folder_path, '1.jpg')

    try:
        with session.get(image_url, stream=True) as r:
            r.raise_for_status()
            with open(image_path, 'wb') as f:
                for chunk in r.iter_content(chunk_size=8192):
                    f.write(chunk)
        logging.info(f"Image saved to {image_path}")
    except Exception as e:
        logging.error(f"Error downloading image: {image_url} with error {e}")

def process_url(session, id, url):
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3'}
    try:
        response = session.get(url, headers=headers, timeout=10)
        soup = BeautifulSoup(response.content, 'html.parser')
        twitter_image = soup.find('meta', attrs={'name': 'twitter:image'})
        if twitter_image and twitter_image.has_attr('content'):
            image_url = twitter_image['content']
            download_image(session, id, image_url)
        else:
            logging.error(f"No 'twitter:image' meta tag found for URL: {url}")
    except requests.exceptions.RequestException as e:
        logging.error(f"Error processing URL: {url} with error {e}")

def main():
    session = setup_session()
    with open('images.txt', 'r') as file:
        for line in file:
            id, url = line.strip().split(',')
            logging.info(f"Processing {id} with URL: {url}")
            process_url(session, id, url)

if __name__ == "__main__":
    main()
