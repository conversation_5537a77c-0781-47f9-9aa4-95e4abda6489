import requests
from bs4 import BeautifulSoup
import re
import json


def sanitize(text, is_numeric=False):
    if is_numeric:
        cleaned_text = re.sub(r"[^\d.]+", "", text)
        if cleaned_text.count(".") > 1:
            parts = cleaned_text.split(".")
            cleaned_text = parts[0] + "." + "".join(parts[1:]).replace(".", "")
        return cleaned_text
    else:
        return text.replace(",", "").strip()


def find_dimension_value(dimensions_map, possible_labels):
    for label in possible_labels:
        if label in dimensions_map:
            return sanitize(dimensions_map[label], is_numeric=True)
    return "Not Available"


def extract_rating(input_string):
    match = re.search(r"\d+\.?\d*", input_string)

    return match.group(0) if match else None


def extract_reviews(text):
    match = re.search(r"\d+", text)

    return match.group(0) if match else None


def scrape_section(soup, section):
    dimensions_header = soup.find("h3", string=section)
    dimensions_array = []
    if dimensions_header:
        table = dimensions_header.find_next_sibling("table")
        if table:
            rows = table.find_all("tr")
            for row in rows:
                key_td = row.find("td")
                key_span = key_td.find("span") if key_td else None
                key = sanitize(key_span.text) if key_span else None
                value_td = key_td.find_next_sibling("td") if key_td else None
                value = value_td.text if value_td else None
                if key and value:
                    dimensions_array.append({"specName": key, "specValue": value})
    return dimensions_array


def scrape_product_info(id, url):
    response = requests.get(url)
    soup = BeautifulSoup(response.text, "html.parser")
    specificationGroups = []

    sections = [
        "Dimensions and Measurements",
        "Included Components",
        "Characteristics and Features",
        "Warranty and Product Information",
    ]

    for section in sections:
        specificationGroup = {}
        specificationGroup["specTitle"] = section
        specs = scrape_section(soup, section)

        specificationGroup["specifications"] = specs
        specificationGroups.append(specificationGroup)

    # spec_groups = soup.find_all("div", class_="dn db-ns")
    # print(spec_groups)

    # for sec_group in spec_groups:
    #     specificationGroup = {}
    #     header = sec_group.find("div").find("h3")
    #     if not header:
    #         continue

    #     specificationGroup["specTitle"] = header.text
    #     dimensions_array = []
    #     table = sec_group.find("div").find("table")
    #     if table:
    #         rows = table.find_all("tr")
    #         for row in rows:
    #             key_td = row.find("td")
    #             key_span = key_td.find("span") if key_td else None
    #             key = sanitize(key_span.text) if key_span else None
    #             value_td = key_td.find_next_sibling("td") if key_td else None
    #             value = value_td.text if value_td else None
    #             if key and value:
    #                 dimensions_array.append({"specName": key, "specValue": value})

    #     specificationGroup["specifications"] = dimensions_array
    #     specificationGroups.append(specificationGroup)

    desc_div = soup.find("div", class_="lh-copy H_oFW")
    description = sanitize(desc_div.text) if desc_div else ""
    ratingDiv = soup.find("div", class_="star-rating")
    reviewsDiv = soup.find("div", class_="f6 pl2 dn db-ns")
    averageRating = "0"
    totalReviews = "0"

    if reviewsDiv:
        totalReviews = extract_reviews(reviewsDiv.find("button").text)
    else:
        reviewsDiv = soup.find("div", class_="flex flex-row items-center f6")
        if reviewsDiv:
            totalReviews = extract_reviews(reviewsDiv.find("a").text)

    if ratingDiv:
        averageRating = extract_rating(ratingDiv["style"]) if ratingDiv.has_attr("style") else None

    jsonDict = {
        "id": id,
        "highlights": [],
        "description": description,
        "specificationGroup": specificationGroups,
        "totalReviews": totalReviews,
        "averageRating": averageRating,
    }
    return json.dumps(jsonDict)


if __name__ == "__main__":
    with open("urls3.txt", "r", encoding="utf-8") as urls_file:
        lines = urls_file.read().splitlines()

    with open("product_data3.csv", "w", encoding="utf-8") as output_file:
        output_file.write("[")
        for line in lines:
            id, url = line.strip().split(",")
            jsonString = scrape_product_info(id, url)
            output_file.write(f"{jsonString},\n")

        output_file.write("]")


# "id": "a10f7dc0-9864-4ab3-b222-3214f92554b5",
# "description": "the description",
# "highlights": [
#    "Features continuous self-test funtionality",
#    "Tamper-resistant",
#    "Available in 27 colors"
# ],
# "averageRating": "4.5273",
# "totalReviews": "55",
# "specificationGroup": [
#  {
#     "specTitle": "Details",
#     "specifications": [
#     {
#         "specName": "Amperage (A)",
#         "specValue": "15 A"
#     }
#   }
# ]
