import axios from 'axios'
import * as fs from 'fs'

interface HomeDepotProduct {
  id: string
  sku: string
}

interface GraphQLResponse {
  // Define the structure of your GraphQL response here
}

interface ProductResponse {
  id: string
  graphqlResponse: GraphQLResponse
}

async function fetchProductData(itemId: string) {
  const endpoint = 'https://www.homedepot.com/federation-gateway/graphql?opname=productClientOnlyProduct'
  const query = `query productClientOnlyProduct($storeId: String, $zipCode: String, $quantity: Int, $itemId: String!, $dataSource: String, $loyaltyMembershipInput: LoyaltyMembershipInput, $skipSpecificationGroup: Boolean = false, $skipSubscribeAndSave: Boolean = false, $skipInstallServices: Boolean = true, $skipKPF: Boolean = false) {
        product(itemId: $itemId, dataSource: $dataSource, loyaltyMembershipInput: $loyaltyMembershipInput) {
          fulfillment(storeId: $storeId, zipCode: $zipCode, quantity: $quantity) {
            backordered
          fulfillmentOptions {
              type
            services {
                type
              deliveryTimeline
              deliveryDates {
                  startDate
                endDate
                __typename
              }
              deliveryCharge
              dynamicEta {
                  hours
                minutes
                __typename
              }
              hasFreeShipping
              freeDeliveryThreshold
              locations {
                  curbsidePickupFlag
                isBuyInStoreCheckNearBy
                distance
                inventory {
                    isOutOfStock
                  isInStock
                  isLimitedQuantity
                  isUnavailable
                  quantity
                  maxAllowedBopisQty
                  minAllowedBopisQty
                  __typename
                }
                isAnchor
                locationId
                state
                storeName
                storePhone
                type
                storeTimeZone
                __typename
              }
              totalCharge
              optimalFulfillment
              __typename
            }
            fulfillable
            __typename
          }
          anchorStoreStatus
          anchorStoreStatusType
          backorderedShipDate
          bossExcludedShipStates
          excludedShipStates
          seasonStatusEligible
          onlineStoreStatus
          onlineStoreStatusType
          fallbackMode
          sthExcludedShipState
          bossExcludedShipState
          inStoreAssemblyEligible
          bodfsAssemblyEligible
          __typename
        }
        info {
            dotComColorEligible
          hidePrice
          ecoRebate
          quantityLimit
          sskMin
          sskMax
          unitOfMeasureCoverage
          wasMaxPriceRange
          wasMinPriceRange
          productSubType {
              name
            link
            __typename
          }
          fiscalYear
          productDepartment
          classNumber
          forProfessionalUseOnly
          globalCustomConfigurator {
              customButtonText
            customDescription
            customExperience
            customExperienceUrl
            customTitle
            __typename
          }
          paintBrand
          movingCalculatorEligible
          label
          isSponsored
          sponsoredMetadata {
              campaignId
            placementId
            slotId
            __typename
          }
          sponsoredBeacon {
              onClickBeacon
            onViewBeacon
            __typename
          }
          augmentedReality
          isLiveGoodsProduct
          hasSubscription
          isBuryProduct
          isGenericProduct
          returnable
          categoryHierarchy
          samplesAvailable
          customerSignal {
              previouslyPurchased
            __typename
          }
          productDepartmentId
          swatches {
              isSelected
            itemId
            label
            swatchImgUrl
            url
            value
            __typename
          }
          totalNumberOfOptions
          gccExperienceOmsId
          recommendationFlags {
              visualNavigation
            pipCollections
            packages
            ACC
            frequentlyBoughtTogether
            bundles
            __typename
          }
          replacementOMSID
          minimumOrderQuantity
          projectCalculatorEligible
          subClassNumber
          calculatorType
          pipCalculator {
              coverageUnits
            display
            publisher
            toggle
            __typename
          }
          protectionPlanSku
          eligibleProtectionPlanSkus
          hasServiceAddOns
          consultationType
          __typename
        }
        identifiers {
            skuClassification
          canonicalUrl
          brandName
          itemId
          modelNumber
          productLabel
          storeSkuNumber
          upcGtin13
          specialOrderSku
          toolRentalSkuNumber
          rentalCategory
          rentalSubCategory
          upc
          productType
          isSuperSku
          parentId
          roomVOEnabled
          sampleId
          __typename
        }
        itemId
        dataSources
        availabilityType {
            discontinued
          status
          type
          buyable
          __typename
        }
        details {
            description
          collection {
              url
            collectionId
            name
            __typename
          }
          highlights
          installation {
              leadGenUrl
            __typename
          }
          __typename
        }
        media {
            images {
              url
            type
            subType
            sizes
            altText
            __typename
          }
          video {
              shortDescription
            thumbnail
            url
            videoStill
            link {
                text
              url
              __typename
            }
            title
            type
            videoId
            longDescription
            __typename
          }
          threeSixty {
              id
            url
            __typename
          }
          augmentedRealityLink {
              usdz
            image
            __typename
          }
          __typename
        }
        pricing(storeId: $storeId) {
            promotion {
              dates {
                end
              start
              __typename
            }
            type
            description {
                shortDesc
              longDesc
              __typename
            }
            dollarOff
            percentageOff
            promotionTag
            savingsCenter
            savingsCenterPromos
            specialBuySavings
            specialBuyDollarOff
            specialBuyPercentageOff
            experienceTag
            subExperienceTag
            __typename
          }
          value
          alternatePriceDisplay
          alternate {
              bulk {
                pricePerUnit
              thresholdQuantity
              value
              __typename
            }
            unit {
                caseUnitOfMeasure
              unitsOriginalPrice
              unitsPerCase
              value
              __typename
            }
            __typename
          }
          original
          mapAboveOriginalPrice
          message
          preferredPriceFlag
          specialBuy
          unitOfMeasure
          conditionalPromotions {
              dates {
                start
              end
              __typename
            }
            description {
                shortDesc
              longDesc
              __typename
            }
            experienceTag
            subExperienceTag
            eligibilityCriteria {
                itemGroup
              minPurchaseAmount
              minPurchaseQuantity
              relatedSkusCount
              omsSkus
              __typename
            }
            reward {
                tiers {
                  minPurchaseAmount
                minPurchaseQuantity
                rewardPercent
                rewardAmountPerOrder
                rewardAmountPerItem
                rewardFixedPrice
                maxAllowedRewardAmount
                maxPurchaseQuantity
                __typename
              }
              __typename
            }
            nvalues
            brandRefinementId
            __typename
          }
          __typename
        }
        reviews {
            ratingsReviews {
              averageRating
            totalReviews
            __typename
          }
          __typename
        }
        seo {
            seoKeywords
          seoDescription
          __typename
        }
        specificationGroup @skip(if: $skipSpecificationGroup) {
            specifications {
              specName
            specValue
            __typename
          }
          specTitle @skip(if: $skipSpecificationGroup)
          __typename
        }
        taxonomy {
            breadCrumbs {
              label
            url
            browseUrl
            creativeIconUrl
            deselectUrl
            dimensionName
            refinementKey
            __typename
          }
          brandLinkUrl
          __typename
        }
        favoriteDetail {
            count
          __typename
        }
        sizeAndFitDetail {
            attributeGroups {
              attributes {
                attributeName
              dimensions
              __typename
            }
            dimensionLabel
            productType
            __typename
          }
          __typename
        }
        subscription @skip(if: $skipSubscribeAndSave) {
            defaultfrequency @skip(if: $skipSubscribeAndSave)
          discountPercentage @skip(if: $skipSubscribeAndSave)
          subscriptionEnabled @skip(if: $skipSubscribeAndSave)
          __typename
        }
        badges(storeId: $storeId) {
            label
          name
          color
          creativeImageUrl
          endDate
          message
          timerDuration
          timer {
              timeBombThreshold
            daysLeftThreshold
            dateDisplayThreshold
            message
            __typename
          }
          __typename
        }
        dataSource
        installServices(storeId: $storeId, zipCode: $zipCode) @skip(if: $skipInstallServices) {
            scheduleAMeasure @skip(if: $skipInstallServices)
          gccCarpetDesignAndOrderEligible @skip(if: $skipInstallServices)
          __typename
        }
        keyProductFeatures @skip(if: $skipKPF) {
            keyProductFeaturesItems {
              features {
                name
              refinementId
              refinementUrl
              value
              __typename
            }
            __typename
          }
          __typename
        }
        seoDescription
        __typename
      }
    }`

  const variables = {
    skipSpecificationGroup: false,
    skipSubscribeAndSave: false,
    skipInstallServices: true,
    skipKPF: false,
    itemId: itemId,
    storeId: '1002',
    zipCode: '90255',
  }

  const response = await axios.post(
    endpoint,
    {
      query: query,
      variables: variables,
    },
    {
      headers: {
        'x-experience-name': 'general-merchandise',
        'Content-Type': 'application/json',
      },
    },
  )

  return response.data
}

function sleep(ms: number) {
  return new Promise((resolve) => setTimeout(resolve, ms))
}

async function processAllProducts() {
  const products: HomeDepotProduct[] = JSON.parse(fs.readFileSync('homeDepotProducts.json', 'utf8'))
  const productResponses: ProductResponse[] = []

  for (const product of products) {
    try {
      const graphqlResponse = await fetchProductData(product.sku)
      productResponses.push({
        id: product.id,
        graphqlResponse: graphqlResponse,
      })
      await sleep(2000)
    } catch (error) {
      console.error(`Error fetching data for SKU ${product.sku}:`, error)
    }
  }

  fs.writeFileSync('productDetails.json', JSON.stringify(productResponses, null, 2))
  console.log('Product details saved to productDetails.json')
}

processAllProducts().then(() => console.log('All product data fetched.'))
