'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
var fs = require('fs')
function checkDataIntegrity(product) {
  if (!product.graphqlResponse || !product.graphqlResponse.data || !product.graphqlResponse.data.product) {
    console.error('Product ID '.concat(product.id, " is missing required 'data.product' structure."))
    return false
  }
  var _a = product.graphqlResponse.data.product,
    details = _a.details,
    reviews = _a.reviews,
    specificationGroup = _a.specificationGroup
  if (
    !details ||
    typeof details.description !== 'string' ||
    (details.highlights !== null && !Array.isArray(details.highlights))
  ) {
    console.error('Product ID '.concat(product.id, " has invalid 'details' structure."))
    return false
  }
  if (reviews !== null) {
    if (
      !reviews ||
      !reviews.ratingsReviews ||
      (reviews.ratingsReviews.averageRating !== null && typeof reviews.ratingsReviews.averageRating !== 'string') ||
      typeof reviews.ratingsReviews.totalReviews !== 'string'
    ) {
      console.error('Product ID '.concat(product.id, " has invalid 'reviews' structure."))
      return false
    }
  }
  if (!Array.isArray(specificationGroup)) {
    console.error('Product ID '.concat(product.id, " 'specificationGroup' is not an array."))
    return false
  }
  for (var _i = 0, specificationGroup_1 = specificationGroup; _i < specificationGroup_1.length; _i++) {
    var specGroup = specificationGroup_1[_i]
    if (typeof specGroup.specTitle !== 'string' || !Array.isArray(specGroup.specifications)) {
      console.error('Product ID '.concat(product.id, " has invalid 'specificationGroup' structure."))
      return false
    }
    for (var _b = 0, _c = specGroup.specifications; _b < _c.length; _b++) {
      var spec = _c[_b]
      if (typeof spec.specName !== 'string' || typeof spec.specValue !== 'string') {
        console.error(
          'Product ID '.concat(product.id, " has invalid 'specifications' structure in 'specificationGroup'."),
        )
        return false
      }
    }
  }
  return true
}
function processData() {
  var productResponses = JSON.parse(fs.readFileSync('productDetails.json', 'utf8'))
  var allDataValid = true
  for (var _i = 0, productResponses_1 = productResponses; _i < productResponses_1.length; _i++) {
    var product = productResponses_1[_i]
    if (!checkDataIntegrity(product)) {
      allDataValid = false
    }
  }
  if (allDataValid) {
    console.log('All product data passed integrity checks.')
  } else {
    console.log('Some product data failed integrity checks. See error messages.')
  }
}
processData()
