import axios from 'axios'
import * as fs from 'fs'

interface Vendor {
  name: string
}

interface Availability {
  vendor: Vendor
  sku: string
}

interface Product {
  id: string
  availableFrom: Availability[]
}

interface ApiResponse {
  data: Product[]
  pagination: {
    currentPage: number
    lastPage: number
  }
}

async function fetchProducts(page: number = 1): Promise<ApiResponse> {
  const url = `http://api.averyapi.com/v2/materials/vendors/bc550289-78cc-4a69-8a46-175ebe1e5753/products?currentPage=${page}`
  const response = await axios.get<ApiResponse>(url)
  return response.data
}

async function processProducts() {
  let currentPage = 1
  let lastPage = 0
  const homeDepotProducts: { id: string; sku: string }[] = []

  do {
    const response = await fetchProducts(currentPage)
    response.data.forEach((product) => {
      product.availableFrom.forEach((avail) => {
        if (avail.vendor.name === 'The Home Depot') {
          homeDepotProducts.push({ id: product.id, sku: avail.sku })
        }
      })
    })

    currentPage = response.pagination.currentPage + 1
    lastPage = response.pagination.lastPage
  } while (currentPage <= lastPage)

  fs.writeFileSync('homeDepotProducts.json', JSON.stringify(homeDepotProducts, null, 2))
}

processProducts().then(() => console.log('Data fetched and saved.'))
