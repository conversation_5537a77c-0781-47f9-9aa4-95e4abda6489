import requests
from bs4 import BeautifulSoup
import re
import json

spec_keys = { 
    'Item Code': 'ProductCode',
    'Material': 'MaterialType',
    'Series Name': 'ProductSeries',
    'Series Color': 'SeriesColor',
    'Actual Size': 'ActualSize',
    'Nominal Size': 'Size',
    'Thickness': 'Thickness',
    'Country of Origin': 'CountryOfOrigin',
    'Shape': 'Shape' 
}

package_keys = {

}

def scrape_product_info(id, url):
    response = requests.get(url)
    soup = BeautifulSoup(response.text, 'html.parser')
    script_tags = soup.find_all('script', text=lambda t: t and 'window.bdApp.value' in t)

    # Check if a matching script tag was found
    if script_tags:
        # Assuming the first match contains the payload
        js_code = script_tags[0].string
        
        # Locate the JSON object within the JavaScript code
        start = js_code.find('{')  # Assuming the JSON object starts with '{'
        end = js_code.rfind('}')  # Assuming the JSON object ends with '}'
        json_str = js_code[start:end+1] if start != -1 and end != -1 else ''
        
        # Convert the JSON string to a Python dictionary
        payload_dict = json.loads(json_str) if json_str else {}
    else:
        # Handle the case where no matching script tag was found
        payload_dict = {}
        print("No matching script tag found.")

    specificationGroups = []
    specificationGroup = {}
    specificationGroup['specTitle'] = 'Specifications'
    specs = []
    for key, value in spec_keys.items():
        specs.append({'specName': key, 'specValue': payload_dict['Product'][value]})
    specificationGroup['specifications'] = specs
    specificationGroups.append(specificationGroup)

    specificationGroup = {}
    specificationGroup['specTitle'] = 'Packaging'
    specs = []
    for value in payload_dict['Packaging']:
        specs.append({'specName': value['Key'], 'specValue': value['Value']})
    specificationGroup['specifications'] = specs
    specificationGroups.append(specificationGroup)

    jsonDict = {'id': id, 
        'highlights': [],
        'description': payload_dict['Product']['Description'], 
        'specificationGroup': specificationGroups, 
        'totalReviews': '0', 
        'averageRating': '0' }
    return json.dumps(jsonDict)

if __name__ == '__main__':
    with open('urls.txt', 'r', encoding='utf-8') as urls_file:
        lines = urls_file.read().splitlines()
    
    with open('product_data.csv', 'w', encoding='utf-8') as output_file:
        output_file.write('[')
        for line in lines:
            id, url = line.strip().split(',')
            jsonString = scrape_product_info(id, url)
            output_file.write(f'{jsonString},\n')

        output_file.write(']')
