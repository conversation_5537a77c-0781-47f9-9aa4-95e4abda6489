'use strict'
var __awaiter =
  (this && this.__awaiter) ||
  function (thisArg, _arguments, P, generator) {
    function adopt(value) {
      return value instanceof P
        ? value
        : new P(function (resolve) {
            resolve(value)
          })
    }
    return new (P || (P = Promise))(function (resolve, reject) {
      function fulfilled(value) {
        try {
          step(generator.next(value))
        } catch (e) {
          reject(e)
        }
      }
      function rejected(value) {
        try {
          step(generator['throw'](value))
        } catch (e) {
          reject(e)
        }
      }
      function step(result) {
        result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected)
      }
      step((generator = generator.apply(thisArg, _arguments || [])).next())
    })
  }
var __generator =
  (this && this.__generator) ||
  function (thisArg, body) {
    var _ = {
        label: 0,
        sent: function () {
          if (t[0] & 1) throw t[1]
          return t[1]
        },
        trys: [],
        ops: [],
      },
      f,
      y,
      t,
      g
    return (
      (g = { next: verb(0), throw: verb(1), return: verb(2) }),
      typeof Symbol === 'function' &&
        (g[Symbol.iterator] = function () {
          return this
        }),
      g
    )
    function verb(n) {
      return function (v) {
        return step([n, v])
      }
    }
    function step(op) {
      if (f) throw new TypeError('Generator is already executing.')
      while ((g && ((g = 0), op[0] && (_ = 0)), _))
        try {
          if (
            ((f = 1),
            y &&
              (t = op[0] & 2 ? y['return'] : op[0] ? y['throw'] || ((t = y['return']) && t.call(y), 0) : y.next) &&
              !(t = t.call(y, op[1])).done)
          )
            return t
          if (((y = 0), t)) op = [op[0] & 2, t.value]
          switch (op[0]) {
            case 0:
            case 1:
              t = op
              break
            case 4:
              _.label++
              return { value: op[1], done: false }
            case 5:
              _.label++
              y = op[1]
              op = [0]
              continue
            case 7:
              op = _.ops.pop()
              _.trys.pop()
              continue
            default:
              if (!((t = _.trys), (t = t.length > 0 && t[t.length - 1])) && (op[0] === 6 || op[0] === 2)) {
                _ = 0
                continue
              }
              if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) {
                _.label = op[1]
                break
              }
              if (op[0] === 6 && _.label < t[1]) {
                _.label = t[1]
                t = op
                break
              }
              if (t && _.label < t[2]) {
                _.label = t[2]
                _.ops.push(op)
                break
              }
              if (t[2]) _.ops.pop()
              _.trys.pop()
              continue
          }
          op = body.call(thisArg, _)
        } catch (e) {
          op = [6, e]
          y = 0
        } finally {
          f = t = 0
        }
      if (op[0] & 5) throw op[1]
      return { value: op[0] ? op[1] : void 0, done: true }
    }
  }
Object.defineProperty(exports, '__esModule', { value: true })
var axios_1 = require('axios')
var fs = require('fs')
function fetchProductData(itemId) {
  return __awaiter(this, void 0, void 0, function () {
    var endpoint, query, variables, response
    return __generator(this, function (_a) {
      switch (_a.label) {
        case 0:
          endpoint = 'https://www.homedepot.com/federation-gateway/graphql?opname=productClientOnlyProduct'
          query =
            'query productClientOnlyProduct($storeId: String, $zipCode: String, $quantity: Int, $itemId: String!, $dataSource: String, $loyaltyMembershipInput: LoyaltyMembershipInput, $skipSpecificationGroup: Boolean = false, $skipSubscribeAndSave: Boolean = false, $skipInstallServices: Boolean = true, $skipKPF: Boolean = false) {\n        product(itemId: $itemId, dataSource: $dataSource, loyaltyMembershipInput: $loyaltyMembershipInput) {\n          fulfillment(storeId: $storeId, zipCode: $zipCode, quantity: $quantity) {\n            backordered\n          fulfillmentOptions {\n              type\n            services {\n                type\n              deliveryTimeline\n              deliveryDates {\n                  startDate\n                endDate\n                __typename\n              }\n              deliveryCharge\n              dynamicEta {\n                  hours\n                minutes\n                __typename\n              }\n              hasFreeShipping\n              freeDeliveryThreshold\n              locations {\n                  curbsidePickupFlag\n                isBuyInStoreCheckNearBy\n                distance\n                inventory {\n                    isOutOfStock\n                  isInStock\n                  isLimitedQuantity\n                  isUnavailable\n                  quantity\n                  maxAllowedBopisQty\n                  minAllowedBopisQty\n                  __typename\n                }\n                isAnchor\n                locationId\n                state\n                storeName\n                storePhone\n                type\n                storeTimeZone\n                __typename\n              }\n              totalCharge\n              optimalFulfillment\n              __typename\n            }\n            fulfillable\n            __typename\n          }\n          anchorStoreStatus\n          anchorStoreStatusType\n          backorderedShipDate\n          bossExcludedShipStates\n          excludedShipStates\n          seasonStatusEligible\n          onlineStoreStatus\n          onlineStoreStatusType\n          fallbackMode\n          sthExcludedShipState\n          bossExcludedShipState\n          inStoreAssemblyEligible\n          bodfsAssemblyEligible\n          __typename\n        }\n        info {\n            dotComColorEligible\n          hidePrice\n          ecoRebate\n          quantityLimit\n          sskMin\n          sskMax\n          unitOfMeasureCoverage\n          wasMaxPriceRange\n          wasMinPriceRange\n          productSubType {\n              name\n            link\n            __typename\n          }\n          fiscalYear\n          productDepartment\n          classNumber\n          forProfessionalUseOnly\n          globalCustomConfigurator {\n              customButtonText\n            customDescription\n            customExperience\n            customExperienceUrl\n            customTitle\n            __typename\n          }\n          paintBrand\n          movingCalculatorEligible\n          label\n          isSponsored\n          sponsoredMetadata {\n              campaignId\n            placementId\n            slotId\n            __typename\n          }\n          sponsoredBeacon {\n              onClickBeacon\n            onViewBeacon\n            __typename\n          }\n          augmentedReality\n          isLiveGoodsProduct\n          hasSubscription\n          isBuryProduct\n          isGenericProduct\n          returnable\n          categoryHierarchy\n          samplesAvailable\n          customerSignal {\n              previouslyPurchased\n            __typename\n          }\n          productDepartmentId\n          swatches {\n              isSelected\n            itemId\n            label\n            swatchImgUrl\n            url\n            value\n            __typename\n          }\n          totalNumberOfOptions\n          gccExperienceOmsId\n          recommendationFlags {\n              visualNavigation\n            pipCollections\n            packages\n            ACC\n            frequentlyBoughtTogether\n            bundles\n            __typename\n          }\n          replacementOMSID\n          minimumOrderQuantity\n          projectCalculatorEligible\n          subClassNumber\n          calculatorType\n          pipCalculator {\n              coverageUnits\n            display\n            publisher\n            toggle\n            __typename\n          }\n          protectionPlanSku\n          eligibleProtectionPlanSkus\n          hasServiceAddOns\n          consultationType\n          __typename\n        }\n        identifiers {\n            skuClassification\n          canonicalUrl\n          brandName\n          itemId\n          modelNumber\n          productLabel\n          storeSkuNumber\n          upcGtin13\n          specialOrderSku\n          toolRentalSkuNumber\n          rentalCategory\n          rentalSubCategory\n          upc\n          productType\n          isSuperSku\n          parentId\n          roomVOEnabled\n          sampleId\n          __typename\n        }\n        itemId\n        dataSources\n        availabilityType {\n            discontinued\n          status\n          type\n          buyable\n          __typename\n        }\n        details {\n            description\n          collection {\n              url\n            collectionId\n            name\n            __typename\n          }\n          highlights\n          installation {\n              leadGenUrl\n            __typename\n          }\n          __typename\n        }\n        media {\n            images {\n              url\n            type\n            subType\n            sizes\n            altText\n            __typename\n          }\n          video {\n              shortDescription\n            thumbnail\n            url\n            videoStill\n            link {\n                text\n              url\n              __typename\n            }\n            title\n            type\n            videoId\n            longDescription\n            __typename\n          }\n          threeSixty {\n              id\n            url\n            __typename\n          }\n          augmentedRealityLink {\n              usdz\n            image\n            __typename\n          }\n          __typename\n        }\n        pricing(storeId: $storeId) {\n            promotion {\n              dates {\n                end\n              start\n              __typename\n            }\n            type\n            description {\n                shortDesc\n              longDesc\n              __typename\n            }\n            dollarOff\n            percentageOff\n            promotionTag\n            savingsCenter\n            savingsCenterPromos\n            specialBuySavings\n            specialBuyDollarOff\n            specialBuyPercentageOff\n            experienceTag\n            subExperienceTag\n            __typename\n          }\n          value\n          alternatePriceDisplay\n          alternate {\n              bulk {\n                pricePerUnit\n              thresholdQuantity\n              value\n              __typename\n            }\n            unit {\n                caseUnitOfMeasure\n              unitsOriginalPrice\n              unitsPerCase\n              value\n              __typename\n            }\n            __typename\n          }\n          original\n          mapAboveOriginalPrice\n          message\n          preferredPriceFlag\n          specialBuy\n          unitOfMeasure\n          conditionalPromotions {\n              dates {\n                start\n              end\n              __typename\n            }\n            description {\n                shortDesc\n              longDesc\n              __typename\n            }\n            experienceTag\n            subExperienceTag\n            eligibilityCriteria {\n                itemGroup\n              minPurchaseAmount\n              minPurchaseQuantity\n              relatedSkusCount\n              omsSkus\n              __typename\n            }\n            reward {\n                tiers {\n                  minPurchaseAmount\n                minPurchaseQuantity\n                rewardPercent\n                rewardAmountPerOrder\n                rewardAmountPerItem\n                rewardFixedPrice\n                maxAllowedRewardAmount\n                maxPurchaseQuantity\n                __typename\n              }\n              __typename\n            }\n            nvalues\n            brandRefinementId\n            __typename\n          }\n          __typename\n        }\n        reviews {\n            ratingsReviews {\n              averageRating\n            totalReviews\n            __typename\n          }\n          __typename\n        }\n        seo {\n            seoKeywords\n          seoDescription\n          __typename\n        }\n        specificationGroup @skip(if: $skipSpecificationGroup) {\n            specifications {\n              specName\n            specValue\n            __typename\n          }\n          specTitle @skip(if: $skipSpecificationGroup)\n          __typename\n        }\n        taxonomy {\n            breadCrumbs {\n              label\n            url\n            browseUrl\n            creativeIconUrl\n            deselectUrl\n            dimensionName\n            refinementKey\n            __typename\n          }\n          brandLinkUrl\n          __typename\n        }\n        favoriteDetail {\n            count\n          __typename\n        }\n        sizeAndFitDetail {\n            attributeGroups {\n              attributes {\n                attributeName\n              dimensions\n              __typename\n            }\n            dimensionLabel\n            productType\n            __typename\n          }\n          __typename\n        }\n        subscription @skip(if: $skipSubscribeAndSave) {\n            defaultfrequency @skip(if: $skipSubscribeAndSave)\n          discountPercentage @skip(if: $skipSubscribeAndSave)\n          subscriptionEnabled @skip(if: $skipSubscribeAndSave)\n          __typename\n        }\n        badges(storeId: $storeId) {\n            label\n          name\n          color\n          creativeImageUrl\n          endDate\n          message\n          timerDuration\n          timer {\n              timeBombThreshold\n            daysLeftThreshold\n            dateDisplayThreshold\n            message\n            __typename\n          }\n          __typename\n        }\n        dataSource\n        installServices(storeId: $storeId, zipCode: $zipCode) @skip(if: $skipInstallServices) {\n            scheduleAMeasure @skip(if: $skipInstallServices)\n          gccCarpetDesignAndOrderEligible @skip(if: $skipInstallServices)\n          __typename\n        }\n        keyProductFeatures @skip(if: $skipKPF) {\n            keyProductFeaturesItems {\n              features {\n                name\n              refinementId\n              refinementUrl\n              value\n              __typename\n            }\n            __typename\n          }\n          __typename\n        }\n        seoDescription\n        __typename\n      }\n    }'
          variables = {
            skipSpecificationGroup: false,
            skipSubscribeAndSave: false,
            skipInstallServices: true,
            skipKPF: false,
            itemId: itemId,
            storeId: '1002',
            zipCode: '90255',
          }
          return [
            4 /*yield*/,
            axios_1.default.post(
              endpoint,
              {
                query: query,
                variables: variables,
              },
              {
                headers: {
                  'x-experience-name': 'general-merchandise',
                  'Content-Type': 'application/json',
                },
              },
            ),
          ]
        case 1:
          response = _a.sent()
          return [2 /*return*/, response.data]
      }
    })
  })
}
function sleep(ms) {
  return new Promise(function (resolve) {
    return setTimeout(resolve, ms)
  })
}
function processAllProducts() {
  return __awaiter(this, void 0, void 0, function () {
    var products, productResponses, _i, products_1, product, graphqlResponse, error_1
    return __generator(this, function (_a) {
      switch (_a.label) {
        case 0:
          products = JSON.parse(fs.readFileSync('homeDepotProducts.json', 'utf8'))
          productResponses = []
          ;(_i = 0), (products_1 = products)
          _a.label = 1
        case 1:
          if (!(_i < products_1.length)) return [3 /*break*/, 7]
          product = products_1[_i]
          _a.label = 2
        case 2:
          _a.trys.push([2, 5, , 6])
          return [4 /*yield*/, fetchProductData(product.sku)]
        case 3:
          graphqlResponse = _a.sent()
          productResponses.push({
            id: product.id,
            graphqlResponse: graphqlResponse,
          })
          return [4 /*yield*/, sleep(2000)]
        case 4:
          _a.sent()
          return [3 /*break*/, 6]
        case 5:
          error_1 = _a.sent()
          console.error('Error fetching data for SKU '.concat(product.sku, ':'), error_1)
          return [3 /*break*/, 6]
        case 6:
          _i++
          return [3 /*break*/, 1]
        case 7:
          fs.writeFileSync('productDetails.json', JSON.stringify(productResponses, null, 2))
          console.log('Product details saved to productDetails.json')
          return [2 /*return*/]
      }
    })
  })
}
processAllProducts().then(function () {
  return console.log('All product data fetched.')
})
