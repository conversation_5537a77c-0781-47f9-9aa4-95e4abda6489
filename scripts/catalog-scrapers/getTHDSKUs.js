'use strict'
var __awaiter =
  (this && this.__awaiter) ||
  function (thisArg, _arguments, P, generator) {
    function adopt(value) {
      return value instanceof P
        ? value
        : new P(function (resolve) {
            resolve(value)
          })
    }
    return new (P || (P = Promise))(function (resolve, reject) {
      function fulfilled(value) {
        try {
          step(generator.next(value))
        } catch (e) {
          reject(e)
        }
      }
      function rejected(value) {
        try {
          step(generator['throw'](value))
        } catch (e) {
          reject(e)
        }
      }
      function step(result) {
        result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected)
      }
      step((generator = generator.apply(thisArg, _arguments || [])).next())
    })
  }
var __generator =
  (this && this.__generator) ||
  function (thisArg, body) {
    var _ = {
        label: 0,
        sent: function () {
          if (t[0] & 1) throw t[1]
          return t[1]
        },
        trys: [],
        ops: [],
      },
      f,
      y,
      t,
      g
    return (
      (g = { next: verb(0), throw: verb(1), return: verb(2) }),
      typeof Symbol === 'function' &&
        (g[Symbol.iterator] = function () {
          return this
        }),
      g
    )
    function verb(n) {
      return function (v) {
        return step([n, v])
      }
    }
    function step(op) {
      if (f) throw new TypeError('Generator is already executing.')
      while (_)
        try {
          if (
            ((f = 1),
            y &&
              (t = op[0] & 2 ? y['return'] : op[0] ? y['throw'] || ((t = y['return']) && t.call(y), 0) : y.next) &&
              !(t = t.call(y, op[1])).done)
          )
            return t
          if (((y = 0), t)) op = [op[0] & 2, t.value]
          switch (op[0]) {
            case 0:
            case 1:
              t = op
              break
            case 4:
              _.label++
              return { value: op[1], done: false }
            case 5:
              _.label++
              y = op[1]
              op = [0]
              continue
            case 7:
              op = _.ops.pop()
              _.trys.pop()
              continue
            default:
              if (!((t = _.trys), (t = t.length > 0 && t[t.length - 1])) && (op[0] === 6 || op[0] === 2)) {
                _ = 0
                continue
              }
              if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) {
                _.label = op[1]
                break
              }
              if (op[0] === 6 && _.label < t[1]) {
                _.label = t[1]
                t = op
                break
              }
              if (t && _.label < t[2]) {
                _.label = t[2]
                _.ops.push(op)
                break
              }
              if (t[2]) _.ops.pop()
              _.trys.pop()
              continue
          }
          op = body.call(thisArg, _)
        } catch (e) {
          op = [6, e]
          y = 0
        } finally {
          f = t = 0
        }
      if (op[0] & 5) throw op[1]
      return { value: op[0] ? op[1] : void 0, done: true }
    }
  }
exports.__esModule = true
var axios_1 = require('axios')
var fs = require('fs')
function fetchProducts(page) {
  if (page === void 0) {
    page = 1
  }
  return __awaiter(this, void 0, void 0, function () {
    var url, response
    return __generator(this, function (_a) {
      switch (_a.label) {
        case 0:
          url =
            'http://api.averyapi.com/v2/materials/vendors/bc550289-78cc-4a69-8a46-175ebe1e5753/products?currentPage='.concat(
              page,
            )
          return [4 /*yield*/, axios_1['default'].get(url)]
        case 1:
          response = _a.sent()
          return [2 /*return*/, response.data]
      }
    })
  })
}
function processProducts() {
  return __awaiter(this, void 0, void 0, function () {
    var currentPage, lastPage, homeDepotProducts, response
    return __generator(this, function (_a) {
      switch (_a.label) {
        case 0:
          currentPage = 1
          lastPage = 0
          homeDepotProducts = []
          _a.label = 1
        case 1:
          return [4 /*yield*/, fetchProducts(currentPage)]
        case 2:
          response = _a.sent()
          response.data.forEach(function (product) {
            product.availableFrom.forEach(function (avail) {
              if (avail.vendor.name === 'The Home Depot') {
                homeDepotProducts.push({ id: product.id, sku: avail.sku })
              }
            })
          })
          currentPage = response.pagination.currentPage + 1
          lastPage = response.pagination.lastPage
          _a.label = 3
        case 3:
          if (currentPage <= lastPage) return [3 /*break*/, 1]
          _a.label = 4
        case 4:
          fs.writeFileSync('homeDepotProducts.json', JSON.stringify(homeDepotProducts, null, 2))
          return [2 /*return*/]
      }
    })
  })
}
processProducts().then(function () {
  return console.log('Data fetched and saved.')
})
