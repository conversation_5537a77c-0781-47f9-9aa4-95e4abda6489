'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
var fs = require('fs')
function reduceData(productResponses) {
  return productResponses.map(function (product) {
    if (!product.graphqlResponse.data.product) {
      return {
        id: product.id,
        description: null,
        highlights: null,
        averageRating: null,
        totalReviews: '0',
        specificationGroup: null,
      }
    }
    var _a = product.graphqlResponse.data.product,
      details = _a.details,
      reviews = _a.reviews,
      specificationGroup = _a.specificationGroup
    var reducedSpecificationGroup = specificationGroup
      ? specificationGroup.map(function (group) {
          return {
            specTitle: group.specTitle,
            specifications: group.specifications.map(function (spec) {
              return {
                specName: spec.specName,
                specValue: spec.specValue,
              }
            }),
          }
        })
      : null
    return {
      id: product.id,
      description: details ? details.description : null,
      highlights: details ? details.highlights : null,
      averageRating: reviews && reviews.ratingsReviews ? reviews.ratingsReviews.averageRating : null,
      totalReviews: reviews && reviews.ratingsReviews ? reviews.ratingsReviews.totalReviews : '0',
      specificationGroup: reducedSpecificationGroup,
    }
  })
}
function processData() {
  var productResponses = JSON.parse(fs.readFileSync('productDetails.json', 'utf8'))
  var reducedData = reduceData(productResponses)
  fs.writeFileSync('reducedProductDetails.json', JSON.stringify(reducedData, null, 2))
  console.log('Reduced product details saved to reducedProductDetails.json')
}
processData()
