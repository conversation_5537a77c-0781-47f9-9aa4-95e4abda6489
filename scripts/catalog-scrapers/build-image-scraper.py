import os
import requests
from bs4 import BeautifulSoup

def download_image(image_url, filepath):
    response = requests.get(image_url)
    if response.status_code == 200:
        with open(filepath, 'wb') as file:
            file.write(response.content)

def process_url(id, url):
    response = requests.get(url)
    soup = BeautifulSoup(response.content, 'html.parser')
    
    # Find the meta tag with property="og:image"
    image_meta_tag = soup.find('meta', property="og:image")
    
    if image_meta_tag:
        image_url = image_meta_tag['content']
        
        directory_path = os.path.join('images', id)
        os.makedirs(directory_path, exist_ok=True)
        
        image_filename = os.path.basename(image_url)
        filepath = os.path.join(directory_path, image_filename)
        
        download_image(image_url, filepath)
    else:
        print(f"No og:image meta tag found for URL: {url}")

if __name__ == '__main__':
    input_filename = 'images_drains.txt'
    
    with open(input_filename, 'r') as file:
        for line in file:
            id, url = line.strip().split(',')
            process_url(id, url)
