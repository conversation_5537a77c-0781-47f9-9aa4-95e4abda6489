import * as fs from 'fs'

interface Specification {
  specName: string
  specValue: string
}

interface SpecificationGroup {
  specTitle: string
  specifications: Specification[]
}

interface RatingsReviews {
  averageRating: string | null
  totalReviews: string
}

interface Reviews {
  ratingsReviews: RatingsReviews | null
}

interface Details {
  description: string
  highlights: string[] | null
}

interface ProductData {
  details: Details
  reviews: Reviews | null
  specificationGroup: SpecificationGroup[]
}

interface GraphQLResponse {
  data: {
    product: ProductData
  }
}

interface ProductResponse {
  id: string
  graphqlResponse: GraphQLResponse
}

function checkDataIntegrity(product: ProductResponse): boolean {
  if (!product.graphqlResponse || !product.graphqlResponse.data || !product.graphqlResponse.data.product) {
    console.error(`Product ID ${product.id} is missing required 'data.product' structure.`)
    return false
  }

  const { details, reviews, specificationGroup } = product.graphqlResponse.data.product

  if (
    !details ||
    typeof details.description !== 'string' ||
    (details.highlights !== null && !Array.isArray(details.highlights))
  ) {
    console.error(`Product ID ${product.id} has invalid 'details' structure.`)
    return false
  }

  if (reviews !== null) {
    if (
      !reviews ||
      !reviews.ratingsReviews ||
      (reviews.ratingsReviews.averageRating !== null && typeof reviews.ratingsReviews.averageRating !== 'string') ||
      typeof reviews.ratingsReviews.totalReviews !== 'string'
    ) {
      console.error(`Product ID ${product.id} has invalid 'reviews' structure.`)
      return false
    }
  }

  if (!Array.isArray(specificationGroup)) {
    console.error(`Product ID ${product.id} 'specificationGroup' is not an array.`)
    return false
  }

  for (const specGroup of specificationGroup) {
    if (typeof specGroup.specTitle !== 'string' || !Array.isArray(specGroup.specifications)) {
      console.error(`Product ID ${product.id} has invalid 'specificationGroup' structure.`)
      return false
    }

    for (const spec of specGroup.specifications) {
      if (typeof spec.specName !== 'string' || typeof spec.specValue !== 'string') {
        console.error(`Product ID ${product.id} has invalid 'specifications' structure in 'specificationGroup'.`)
        return false
      }
    }
  }

  return true
}

function processData() {
  const productResponses: ProductResponse[] = JSON.parse(fs.readFileSync('productDetails.json', 'utf8'))
  let allDataValid = true

  for (const product of productResponses) {
    if (!checkDataIntegrity(product)) {
      allDataValid = false
    }
  }

  if (allDataValid) {
    console.log('All product data passed integrity checks.')
  } else {
    console.log('Some product data failed integrity checks. See error messages.')
  }
}

processData()
