

from datetime import datetime 
import json 
import re 
import requests
import gzip
from bs4 import BeautifulSoup
from urllib.parse import urlparse
import time

zipCode = '94102'

headers = {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:123.0) Gecko/20100101 Firefox/123.0',
        'Accept': '*/*',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        'Referer': 'https://www.allmodern.com/bathroom/pdp/eaton-surface-mount-framed-1-door-medicine-cabinet-with-3-adjustable-shelves-a001038912.html?piid=66077348',
        'content-type': 'application/json',
        'apollographql-client-name': '@wayfair/sf-ui-product-details',
        'apollographql-client-version': '999deaf5145c6dd01f136a50ae59a591399d83b0',
        'X-PARENT-TXID': 'I/cmXGYLV/xQE4FLeFcNAg==',
        'X-WF-WAY': 'true',
        'wf-locale': 'en-US-x-am',
        'Origin': 'https://www.allmodern.com',
        'Connection': 'keep-alive',
    }

def get_product_id_from_url(url):
    url = url.rsplit('#')[0]
    result1 = re.search(f'-(K~)*([^-]*)\.html', url)
    result2 = re.search(f'piid=([^&]*)', url)
    result3 = re.findall(f'PiID%5B%5D=(\d+)', url, re.DOTALL)

    if result3 and result3[0]:
        return f'{result1[2]}_{"_".join(result3)}'
    elif result2 and result2[1]:
        return f'{result1[2]}_{result2[1].replace("%2C", "_")}'
    else:
        return result1[2]


def get_wf_specs(url, qty=1):
    sku = get_product_id_from_url(url)
    parsed_url = urlparse(url)
    print(sku)
    graph_ql_url = f"{parsed_url.scheme}://{parsed_url.netloc}/graphql"
    payload = {
        'operationName': 'specs',
        'variables': {'sku': sku.split('_')[0]},
        'extensions': {
            'persistedQuery': {
                'version': 1,
                'sha256Hash': '731f41b9572fefb3f47cddc6ab143d198903c8475f753210b4fb044c89d912a4'
                }
            }
        }

    response = requests.post(graph_ql_url, json=payload, headers=headers)

    data = response.json()

    specificationGroups = []
    specificationGroup = {}
    specificationGroup['specTitle'] = 'Details'
    specs = []
    for section in data["data"]["productSpecificationSections"]:
        for edge in section["specifications"]["edges"]:
            spec = edge["node"]["specification"]
            label = spec["label"]
            if "selectedChoices" not in spec["value"]:
                continue 
            values = spec["value"]["selectedChoices"]
            for value in values:
                name = value["name"]
                specs.append({ "specName": label, "specValue": name })

    specificationGroup['specifications'] = specs
    specificationGroups.append(specificationGroup)
    return specificationGroups

def scrape_wf_url(url):
    response = requests.get(url, headers=headers)

    soup = BeautifulSoup(response.content, 'html.parser')
    
    description_tag = soup.find('meta', property="og:description")
    description = description_tag['content']

    rating_tag = soup.find('span', class_="ProductRatingNumberWithCount-rating")
    rating = rating_tag.text.strip()

    reviews_tag = rating_tag.find_next_sibling('a')
    reviews = reviews_tag.text.replace('Reviews', '').strip()
    return description, reviews, rating

if __name__ == '__main__':
    with open('urls.txt', 'r', encoding='utf-8') as urls_file:
        lines = urls_file.read().splitlines()
    
    with open('product_data.csv', 'w', encoding='utf-8') as output_file:
        output_file.write('[')
        for line in lines:
            id, url = line.strip().split(',')
            specs = get_wf_specs(url)
            description, totalReviews, averageRating = scrape_wf_url(url)
            jsonDict = {'id': id, 
                'highlights': [],
                'description': description, 
                'specificationGroup': specs, 
                'totalReviews': totalReviews, 
                'averageRating': averageRating }
            output_file.write(f'{json.dumps(jsonDict)},\n')
            time.sleep(3)

        output_file.write(']')
