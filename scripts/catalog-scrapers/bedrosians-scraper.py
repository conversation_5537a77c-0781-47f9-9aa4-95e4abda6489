import requests
from bs4 import BeautifulSoup
import re

def sanitize(text, is_numeric=False):
    if is_numeric:
        cleaned_text = re.sub(r'[^\d.]+', '', text)
        if cleaned_text.count('.') > 1:
            parts = cleaned_text.split('.')
            cleaned_text = parts[0] + '.' + ''.join(parts[1:]).replace('.', '')
        return cleaned_text
    else:
        return text.replace(',', '').strip()

def scrape_product_info(url):
    response = requests.get(url)
    soup = BeautifulSoup(response.text, 'html.parser')
    name = sanitize(soup.find('h1', class_='pdp-page-product-title').text if soup.find('h1', class_='pdp-page-product-title') else 'Not Available')
    model = sanitize(soup.find('p', class_='pdp-page-product-code').text if soup.find('p', class_='pdp-page-product-code') else 'Not Available')
    price = sanitize(soup.find('div', class_='pdp-page-product-price').text if soup.find('div', class_='pdp-page-product-price') else 'Not Available', is_numeric=True)

    return name, model, price

if __name__ == '__main__':
    with open('urls.txt', 'r', encoding='utf-8') as urls_file:
        urls = urls_file.read().splitlines()
    
    with open('product_data.csv', 'w', encoding='utf-8') as output_file:
        for url in urls:
            name, model, price = scrape_product_info(url)
            output_file.write(f'{name},{model},{price}\n')
