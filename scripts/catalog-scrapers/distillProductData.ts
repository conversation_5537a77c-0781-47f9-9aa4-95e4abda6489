import * as fs from 'fs'

interface Specification {
  specName: string
  specValue: string
}

interface SpecificationGroup {
  specTitle: string
  specifications: Specification[]
}

interface ReducedSpecification {
  specName: string
  specValue: string
}

interface ReducedSpecificationGroup {
  specTitle: string
  specifications: ReducedSpecification[]
}

interface RatingsReviews {
  averageRating: string | null
  totalReviews: string
}

interface Reviews {
  ratingsReviews: RatingsReviews | null
}

interface Details {
  description: string
  highlights: string[] | null
}

interface ProductData {
  details: Details
  reviews: Reviews | null
  specificationGroup: SpecificationGroup[]
}

interface GraphQLResponse {
  data: {
    product: ProductData | null
  }
}

interface ProductResponse {
  id: string
  graphqlResponse: GraphQLResponse
}

interface ReducedProductData {
  id: string
  description: string | null
  highlights: string[] | null
  averageRating: string | null
  totalReviews: string
  specificationGroup: ReducedSpecificationGroup[] | null
}

function reduceData(productResponses: ProductResponse[]): ReducedProductData[] {
  return productResponses.map((product) => {
    if (!product.graphqlResponse.data.product) {
      return {
        id: product.id,
        description: null,
        highlights: null,
        averageRating: null,
        totalReviews: '0',
        specificationGroup: null,
      }
    }

    const { details, reviews, specificationGroup } = product.graphqlResponse.data.product

    const reducedSpecificationGroup = specificationGroup
      ? specificationGroup.map((group) => ({
          specTitle: group.specTitle,
          specifications: group.specifications.map((spec) => ({
            specName: spec.specName,
            specValue: spec.specValue,
          })),
        }))
      : null

    return {
      id: product.id,
      description: details ? details.description : null,
      highlights: details ? details.highlights : null,
      averageRating: reviews && reviews.ratingsReviews ? reviews.ratingsReviews.averageRating : null,
      totalReviews: reviews && reviews.ratingsReviews ? reviews.ratingsReviews.totalReviews : '0',
      specificationGroup: reducedSpecificationGroup,
    }
  })
}

function processData() {
  const productResponses: ProductResponse[] = JSON.parse(fs.readFileSync('productDetails.json', 'utf8'))
  const reducedData = reduceData(productResponses)
  fs.writeFileSync('reducedProductDetails.json', JSON.stringify(reducedData, null, 2))
  console.log('Reduced product details saved to reducedProductDetails.json')
}

processData()
