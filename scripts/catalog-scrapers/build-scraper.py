import requests
from bs4 import BeautifulSoup
import re

def sanitize(text, is_numeric=False):
    if is_numeric:
        cleaned_text = re.sub(r'[^\d.]+', '', text)
        if cleaned_text.count('.') > 1:
            parts = cleaned_text.split('.')
            cleaned_text = parts[0] + '.' + ''.join(parts[1:]).replace('.', '')
        return cleaned_text
    else:
        return text.replace(',', '').strip()

def find_dimension_value(dimensions_map, possible_labels):
    for label in possible_labels:
        if label in dimensions_map:
            return sanitize(dimensions_map[label], is_numeric=True)
    return 'Not Available'

def scrape_dimensions_and_measurements(soup):
    dimensions_header = soup.find('h3', string="Dimensions and Measurements")
    dimensions_map = {}
    if dimensions_header:
        table = dimensions_header.find_next_sibling('table')
        if table:
            rows = table.find_all('tr')
            for row in rows:
                key_td = row.find('td')
                key_span = key_td.find('span') if key_td else None
                key = sanitize(key_span.text) if key_span else None
                value_td = key_td.find_next_sibling('td') if key_td else None
                value = value_td.text if value_td else None
                if key and value:
                    dimensions_map[key] = value
    return dimensions_map

def scrape_product_info(url):
    response = requests.get(url)
    soup = BeautifulSoup(response.text, 'html.parser')
    dimensions = scrape_dimensions_and_measurements(soup)
    name = sanitize(soup.find('span', class_='fw2 di-ns').text if soup.find('span', class_='fw2 di-ns') else 'Not Available')
    model = sanitize(soup.find('span', attrs={'data-automation': 'product-model-number'}).text if soup.find('span', attrs={'data-automation': 'product-model-number'}) else 'Not Available')
    price = sanitize(soup.find('span', attrs={'data-automation': 'price'}).text if soup.find('span', attrs={'data-automation': 'price'}) else 'Not Available', is_numeric=True)
    
    # Define possible labels for each dimension
    width_labels = ['Width', 'Overall Width', 'Showerhead Width']
    depth_labels = ['Depth', 'Spout Reach', 'Shower Arm Reach']
    height_labels = ['Height', 'Showerhead Height']
    
    # Find dimension values
    width = find_dimension_value(dimensions, width_labels)
    depth = find_dimension_value(dimensions, depth_labels)
    height = find_dimension_value(dimensions, height_labels)
    
    return name, model, price, width, depth, height

if __name__ == '__main__':
    with open('urls_drains.txt', 'r', encoding='utf-8') as urls_file:
        urls = urls_file.read().splitlines()
    
    with open('product_data_drains.csv', 'w', encoding='utf-8') as output_file:
        for url in urls:
            name, model, price, width, depth, height = scrape_product_info(url)
            output_file.write(f'{name},{model},{price},{width},{depth},{height}\n')
