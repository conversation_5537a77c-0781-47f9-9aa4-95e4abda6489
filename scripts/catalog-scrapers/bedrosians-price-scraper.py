import pandas as pd
import requests
import re
import csv

def fetch_prices(input_csv, output_csv):
    # Load CSV file
    df = pd.read_csv(input_csv)
    
    # Prepare output file
    with open(output_csv, mode='w', newline='') as file:
        writer = csv.writer(file)
        writer.writerow(['UUID', 'Price'])
        
        # Loop through each row in DataFrame
        for index, row in df.iterrows():
            url = row['URL']
            uuid = row['UUID']
            try:
                # Send GET request to the URL
                response = requests.get(url)
                response.raise_for_status()  # Check for HTTP issues
                
                # Search for moqPrice within JavaScript
                moq_price_match = re.search(r'"moqPrice":\s*([0-9\.]+)', response.text)
                if moq_price_match:
                    moq_price = moq_price_match.group(1)
                else:
                    moq_price = 'Not found'
                
                # Write the UUID and found price to output CSV
                writer.writerow([uuid, moq_price])
            except requests.RequestException as e:
                print(f"Failed to fetch URL {url}: {e}")
                writer.writerow([uuid, 'Fetch Failed'])
            except Exception as e:
                print(f"An error occurred for URL {url}: {e}")
                writer.writerow([uuid, 'Error'])

# Example usage
fetch_prices('input.csv', 'output.csv')
