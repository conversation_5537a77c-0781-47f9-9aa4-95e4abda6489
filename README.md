# Avery Bathroom Designer Service

Avery Bathroom Designer Service is a robust API designed to generate bathroom design recommendations based on a static catalog of products. Through diverse product categories, this service offers varied and creative design combinations for bathrooms.

## Purpose

To provide users with a set of 10 design recommendations for bathrooms with each request. These recommendations are composed of product IDs retrieved from a static catalog.

## Infrastructure

The service runs on AWS, making use of the following resources:

- **Amazon ECS (Elastic Container Service)**: The core service which runs our Dockerized application.
- **Elastic Container Registry (ECR)**: AWS's Docker container registry where our Docker images are stored.
- **Fargate**: Ensures serverless compute for containers. Our service uses Fargate to manage and scale the containers.
- **Amazon Route 53**: Provides domain name services, ensuring user-friendly access.
- **Elastic Load Balancing**: Manages the incoming traffic, distributing it across multiple targets.

## Architecture & Design

### Repository Structure

The code repository is structured to keep it modular and maintainable:

- `src/DesignController.ts`: Manages HTTP requests and responses.
- `src/index.ts`: The main entry point where the server is initialized.
- `src/productData.json`: A static data file with pre-fetched product IDs.
- `src/RecommendationService.ts`: Handles the business logic, generating design recommendations.

This design follows the principle of separation of concerns. By decoupling the controller (HTTP handling) from the service (business logic), the code remains modular, easily testable, and extensible.

### Data Initialization

A script located at `/scripts/fetchProductData.ts` fetches product IDs from an external catalog and saves them in a static JSON file. This operation is a one-time process for the prototype phase.

## Running Locally

To run the service locally, assuming you have Postgres running, execute the following commands:

```bash
yarn
export IS_LOCAL=true
yarn dev
```

### API Endpoints

- **POST `/design/recommendations`**: Returns a set of 10 design recommendations.
- **GET `/`**: A health check route, ensuring service availability for load balancing.

## Usage

After deploying and running the service, users can make a POST request to the `/design/recommendations` endpoint to receive design recommendations. Each recommendation includes various product categories, with each category containing a product ID referencing the catalog.

## Deployment

Run the deploy.sh script located in the root of the repository. This script handles building the Docker image and pushing it to ECR.

Use the AWS console to create a new task revision and then update the service to utilize it.

## Notes

1. For the prototype stage, data from the catalog is assumed to be static and is fetched and saved once. Future versions might consider periodic data refresh mechanisms.
2. The current domain for accessing the service is `averyapi.com`.

## Environment Variables

| Variable               | Description                                            |
| ---------------------- | ------------------------------------------------------ |
| IS_LOCAL               | Whether the current deployment is local dev deployment |
| OPENAI_API_KEY         | Open AI key                                            |
| DB_HOST                | Database host                                          |
| DB_PORT                | Database host port                                     |
| DB_USER                | Database user                                          |
| DB_PASSWORD            | Database password                                      |
| DB_NAME                | Database name                                          |
| ALGOLIA_APPLICATION_ID | ALGOLIA application ID                                 |
| ALGOLIA_API_KEY        | ALGOLIA API key                                        |

## DB Schema migrations

1. Create a new migration by running `npm run migration:make <migration_name>`.
1. This will create a new file in the `src/main/migrations` directory.
1. Edit the file to make the necessary changes to the database schema.
1. Run `npm run migration:run` to apply the migration locally.
1. Commit the migration file to the repository; it will be applied automatically in production.
