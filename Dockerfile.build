# Note: not using the latest LTS or stable version of node alpine because it still unsupported by <PERSON><PERSON>.
# We should check the latest image version from time to time to make sure that we upgrade our NodeJS version as well
# see https://hub.docker.com/_/microsoft-playwright
FROM node:20.18.0-alpine as deps
WORKDIR /opt/app

# Copy dependency files first to get use of the Docker layers
COPY .npmrc package.json yarn.lock ./

# Install dependencies
RUN yarn --immutable

# Test the code before building the image
FROM node:20.18.0-alpine as tester
WORKDIR /opt/app

COPY --from=deps /opt/app/node_modules node_modules
COPY . .

RUN yarn test

FROM node:20.18.0-alpine as builder
WORKDIR /opt/app

COPY --from=tester /opt/app .

RUN yarn build

FROM node:20.18.0-alpine as runner
WORKDIR /opt/app

COPY --from=builder /opt/app/.env .
COPY --from=builder /opt/app/dist .
COPY --from=builder /opt/app/node_modules node_modules

CMD ["sh", "-c", "node www/index.js"]
