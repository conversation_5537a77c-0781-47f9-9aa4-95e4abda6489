import { Knex } from 'knex'
import { Pool } from 'pg'

type DbInstance = Pool | Knex

const idCache = new Set<string>()

const prefixToTable = {
  MEM: 'members',
  PRJ: 'projects',
} as const

// Function to generate a readable ID

export function generateReadableId(prefix: keyof typeof prefixToTable): string {
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
  let randomString = ''

  for (let i = 0; i < 8; i++) {
    const randomIndex = Math.floor(Math.random() * characters.length)
    randomString += characters[randomIndex]
  }

  return `${prefix}-${randomString}`
}

export async function initializeIdGeneratorCache(db: DbInstance, idColumn: string = 'id'): Promise<void> {
  if (idCache.size === 0) {
    for (const prefix in prefixToTable) {
      const table = prefixToTable[prefix as keyof typeof prefixToTable]
      console.log(`Initializing ID cache for the ${table} table`)

      let rows = []
      if (isPgPool(db)) {
        // Query using pg.Pool
        const query = `SELECT ${idColumn} FROM ${table}`
        const result = await db.query(query)
        rows = result.rows
      } else {
        // Query using Knex
        rows = await db(table).select(idColumn)
      }

      rows.forEach((row) => idCache.add(row[idColumn]))
      console.log(`Loaded ${idCache.size} existing IDs into the ${table} table cache.`)
    }
  }
}

function isIdUnique(id: string): boolean {
  return !idCache.has(id)
}

function addIdToCache(id: string): void {
  idCache.add(id)
}

function isPgPool(instance: DbInstance): instance is Pool {
  return typeof (instance as Pool).query === 'function'
}
