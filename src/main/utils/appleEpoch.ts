export function getAppleEpochTime(): number {
  // 978307200 is the number of seconds between 1970-01-01 and 2001-01-01
  const APPLE_EPOCH_OFFSET = 978307200

  // Date.now() gives milliseconds since 1970-01-01, so divide by 1000 to get seconds
  const nowInUnixSeconds = Math.floor(Date.now() / 1000)

  // Convert to Apple epoch by subtracting the offset
  return nowInUnixSeconds - APPLE_EPOCH_OFFSET
}
