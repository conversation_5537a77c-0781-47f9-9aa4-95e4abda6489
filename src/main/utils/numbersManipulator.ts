export class NumberManipulator {
  static roundPercentage(value: number, decimalElement: number = 2): number {
    return parseFloat(value.toFixed(decimalElement))
  }

  static roundToNearest(value: number, nearest = 100): number {
    return Math.round(value / nearest) * nearest
  }

  static ceilToNearest(value: number, nearest = 100): number {
    return Math.ceil(value / nearest) * nearest
  }

  static floorToNearest(value: number, nearest = 100): number {
    return Math.floor(value / nearest) * nearest
  }
}
