import CamelCaseConverter from '../ts/CamelCaseConverter'

export function handleFilterQueryParams(query: qs.ParsedQs): Record<string, any[]> {
  const filters: Record<string, any[]> = {}

  const paginationAndSortingKeys = ['perPage', 'currentPage', 'sortBy', 'orderBy']

  for (const [key, value] of Object.entries(query)) {
    if (!paginationAndSortingKeys.includes(key)) {
      filters[key] = String(value).split(';')
    }
  }

  return CamelCaseConverter.toSnakeCaseWithException(filters, '>')
}

export function parseParamIdString(string: string): string[] {
  const separator = ';'
  const ids = string.split(separator)
  return ids
}

export function parseInclude(include: any): string[] {
  const includeArray: string[] = []

  if (Array.isArray(include)) {
    includeArray.push(...include.filter((elem) => typeof elem === 'string'))
  } else if (typeof include === 'string') {
    includeArray.push(include)
  }

  return includeArray
}
