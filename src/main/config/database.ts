import knex from 'knex'
import dbConfig from '../knexfile'

export const db = knex(dbConfig)

export async function initializeDatabase() {
  try {
    console.log('Running database migrations...')
    process.env.IS_LOCAL ? console.log('local run') : await db.migrate.latest()
    console.log('Database migrations completed successfully')
  } catch (error) {
    console.error('Error running database migrations:', error)
    throw error
  }
}
