import OpenAI from 'openai'
import { zodResponseFormat } from 'openai/helpers/zod'
import { z } from 'zod'
import { Design } from '../ts/models/Design'
import axios from 'axios'

let openai: OpenAI | undefined

const SYSTEM_INSTRUCTIONS = `You are a senior virtual interior design assistant with many years of experience. You are meeting with a client and asking them a series of questions about their bathroom renovation and will provide them a design style summary at the end.`
const SYSTEM_INSTRUCTIONS_DESIGN = `You are a senior virtual interior design assistant with many years of experience. You are picking a set of products from within a catalog and will provide them a design style summary at the end based on the overall style of the selected products.`

const PROMPT_PREFIX = `Step 1 - Review the image(s) shared and create a summary of the style of the images, including plumbing finish material, vanity type, mood, color palette and atmosphere.

Step 2 - Then based on the questions and answers provided, create a summary including plumbing finish material, vanity type, mood, color palette and atmosphere.

Step 3 - Combine these styles summaries (with a weighting of 60% toward the image summary, to 40% to the Q+A summary) into a 2-3 word Design Style title (with a limit of 25 characters) describing for their bathroom design and a 200-250 character Design Style Description.  Don't talk about a singular design, but instead a design direction that the bathroom could go in.

Step 4 - Combine these styles summaries (with a weight of 60% images, to 40% answers) into a 2-3 word Design Style title describing for their bathroom design and a 200-250 character Design Style Description.`

const PROMPT_PREFIX_DESIGN = `Step 1 - Review the description for each product and create a summary of the style of the products, including plumbing finish material, vanity type, vanity mounting type, mood, colors, color palette and atmosphere.
Step 2 - Based on the style summary create a 1-3 word Design Style title describing for their bathroom design and a 200-250 character Design Style Description that references elements in the bathroom like colors present, finishes and element mounting types.

For context, you are an experienced bathroom designer. You are helping a novice homeowner select materials for their bathroom remodel. 
This homeowner does not have a ton of design or construction knowledge so it's important to speak in language they undersand and mention features the general public would care about. 
This content is going to go in a software application to help people visualize and buy materials for their remodeling project! You want all the content here to be as personalized as possible, so it gives them confidence in their ability to transact.`

export type DesignQuizResultParams = {
  quiz: object | string
}

const DesignQuizResultResponse = z.object({
  title: z.string(),
  description: z.string(),
})

type TDesignQuizResultResponse = z.infer<typeof DesignQuizResultResponse>

export const initOpenai = () => {
  openai = new OpenAI()
}

export const createDesignTitleAndDescription = async (design: Design): Promise<TDesignQuizResultResponse | void> => {
  const productDescriptions = await buildProductDescriptions(design)

  const completion = await openai?.chat.completions.create({
    model: 'o3-mini',
    messages: [
      { role: 'system', content: SYSTEM_INSTRUCTIONS_DESIGN },
      {
        role: 'user',
        content: buildDesignPrompt(PROMPT_PREFIX_DESIGN, productDescriptions),
      },
    ],
    response_format: zodResponseFormat(DesignQuizResultResponse, 'QuizResult'),
  })

  const responseMessage = completion?.choices[0]?.message

  if (responseMessage?.content) {
    const designTitleAndDescription = JSON.parse(responseMessage.content)

    return {
      title: designTitleAndDescription.title,
      description: designTitleAndDescription.description,
    }
  }

  console.error('Error: No content in response message from AI (ChatGPT)')
}

export const createDesignQuizResult = async (quiz: DesignQuizResultParams['quiz']) => {
  const designQuiz = (typeof quiz === 'string' ? JSON.parse(quiz) : quiz) as DesignQuiz

  const completion = await openai?.chat.completions.create({
    model: 'gpt-4o-2024-08-06',
    messages: [
      { role: 'system', content: SYSTEM_INSTRUCTIONS },
      {
        role: 'user',
        content: buildQuizResultPrompt(PROMPT_PREFIX, designQuiz),
      },
    ],
    response_format: zodResponseFormat(DesignQuizResultResponse, 'QuizResult'),
  })

  return completion?.choices[0]?.message
}

interface DesignQuiz {
  moods: Mood[]
  inspirations: Inspiration[]
  colors: Color[]
  finishes: Finish[]
  spaces: Space[]
  features: Feature[]
  vanities: Vanity[]
  toilets: Toilet[]
  packages: Package[]
}

interface Mood {
  id: string
  isChecked: boolean
  src: string
}

interface Inspiration {
  label: string
  description: string
  imageUrl?: string
  isChecked: boolean
}

interface Color {
  label: string
  secondaryLabel?: string
  isChecked: boolean
  primaryColor?: string
  secondaryColor?: string
  tertiaryColor?: string
  isNotSure?: boolean
  placeholderImage?: string
}

interface Finish {
  label: string
  secondaryLabel?: string
  isChecked: boolean
  primaryColor?: string
  backgroundImage?: string
  placeholderImage?: string
  isNotSure?: boolean
}

interface Space {
  title: string
  icon: string
  isChecked: boolean
}

interface Feature {
  title: string
  isChecked: boolean
  icon: string
}

interface Vanity {
  label: string
  description: string
  imageUrl: string
  isChecked: boolean
  isNotSure?: boolean
}

interface Toilet {
  label: string
  description: string
  imageUrl: string
  isChecked: boolean
  isNotSure?: boolean
}

interface Package {
  label: string
  description: string
  price: string
  isChecked: boolean
}

enum MoodEnum {
  Masonry = 'masonry',
  SleekModern = 'sleek modern',
  DarkMoody = 'dark moody',
  BrightCheerful = 'bright cheerful',
  LightAiry = 'light airy',
  TimelessClassic = 'timeless classic',
  RelaxingSpa = 'relaxing spa',
}

const idToMood: Record<string, MoodEnum> = {
  '1': MoodEnum.Masonry,
  '2': MoodEnum.SleekModern,
  '3': MoodEnum.SleekModern,
  '4': MoodEnum.DarkMoody,
  '5': MoodEnum.SleekModern,
  '6': MoodEnum.BrightCheerful,
  '7': MoodEnum.LightAiry,
  '8': MoodEnum.DarkMoody,
  '9': MoodEnum.DarkMoody,
  '10': MoodEnum.TimelessClassic,
  '11': MoodEnum.SleekModern,
  '12': MoodEnum.LightAiry,
  '13': MoodEnum.RelaxingSpa,
  '14': MoodEnum.BrightCheerful,
  '15': MoodEnum.TimelessClassic,
  '16': MoodEnum.BrightCheerful,
  '17': MoodEnum.RelaxingSpa,
  '18': MoodEnum.LightAiry,
  '19': MoodEnum.BrightCheerful,
}

const checkOption = ({ isChecked }: { isChecked: boolean }) => isChecked

const buildProductDescriptions = async (design: Design) => {
  const idToProduct = {
    ...(design.vanity && { [design.vanity]: 'vanity' }),
    ...(design.faucet && { [design.faucet]: 'faucet' }),
    ...(design.mirror && { [design.mirror]: 'mirror' }),
    ...(design.lighting && { [design.lighting]: 'lighting' }),
    ...(design.toilet && { [design.toilet]: 'toilet' }),
    ...(design.shelves && { [design.shelves]: 'shelves' }),
    ...(design.tub && { [design.tub]: 'tub' }),
    ...(design.tubFiller && { [design.tubFiller]: 'tubFiller' }),
    ...(design.tubDoor && { [design.tubDoor]: 'tubDoor' }),
    ...(design.showerGlass && { [design.showerGlass]: 'showerGlass' }),
    ...(design.showerSystem && { [design.showerSystem]: 'showerSystem' }),
    ...(design.paint && { [design.paint]: 'paint' }),
    ...(design.wallpaper && { [design.wallpaper]: 'wallpaper' }),
    ...(design.wallTile && { [design.wallTile]: 'wallTile' }),
    ...(design.floorTile && { [design.floorTile]: 'floorTile' }),
    ...(design.showerWallTile && { [design.showerWallTile]: 'showerWallTile' }),
    ...(design.showerFloorTile && { [design.showerFloorTile]: 'showerFloorTile' }),
  }

  const productIds = Object.keys(idToProduct)
  const { data: productData } = await axios.get(
    `https://api.averyapi.com/catalog/v2/products/renderable-products/${productIds.join(';')}?include[]=details`,
  )
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const products: any[] = productData.data

  const productDescriptions = products.reduce(
    (acc, product) => ({ ...acc, [idToProduct[product.id]]: product.details.description }),
    {} as Record<keyof Design, string>,
  )

  return productDescriptions
}

const buildDesignPrompt = (promptPrefix: string, productDescriptions: Record<keyof Design, string>) => {
  return `${promptPrefix}
  
  ${Object.keys(productDescriptions)
    .map((product) => `${product} description: ${productDescriptions[product as keyof Design]}`)
    .join('\n')}
  `
}

const buildQuizResultPrompt = (promptPrefix: string, designQuiz: DesignQuiz) => {
  const selectedMoods = designQuiz.moods.filter(checkOption).map(({ id }) => idToMood[id])
  const moodPrompt = `What type of mood are you going for? ${selectedMoods.join(' and ')}.`

  const selectedColors = designQuiz.colors
    .filter(checkOption)
    .map(({ label, secondaryLabel }) => `${label} ${secondaryLabel}`)
  const colorPrompt = `What colors are you envisioning? ${selectedColors.join(' and ')}.`

  const selectedFinishes = designQuiz.finishes.filter(checkOption).map(({ label }) => label)
  const finishesPrompt = `What finishes do you prefer? ${selectedFinishes.join(' and ')}`

  const selectedWhoIsUsing = designQuiz.spaces.filter(checkOption).map(({ title }) => title)
  const whoIsUsingPrompt = `Who is using the space? ${selectedWhoIsUsing.join(' and ')}`

  const selectedKindOfFeatures = designQuiz.features.filter(checkOption).map(({ title }) => title)
  const kindOfFeaturesPrompt = `What kind of features do you want? ${selectedKindOfFeatures.join(' and ')}`

  const selectedTypeOfVanities = designQuiz.vanities
    .filter(checkOption)
    .map(({ label, description }) => `${label}; ${description}`)
  const typeOfVanitiesPrompt = `What type of vanities do you prefer? ${selectedTypeOfVanities.join(' and ')}`

  const selectedTypeOfToilets = designQuiz.toilets
    .filter(checkOption)
    .map(({ label, description }) => `${label}; ${description}`)
  const typeOfToiletPrompt = `What type of toilets do you prefer? ${selectedTypeOfToilets.join(' and ')}`

  return `${promptPrefix}
  
  ${moodPrompt}
  ${colorPrompt}
  ${finishesPrompt}
  ${whoIsUsingPrompt}
  ${kindOfFeaturesPrompt}
  ${typeOfVanitiesPrompt}
  ${typeOfToiletPrompt}`
}
