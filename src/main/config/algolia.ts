import algoliasearch, { SearchIndex } from 'algoliasearch'
import {
  FACET_FIELDS_BY_INDEX,
  NUMERIC_FIELDS_BY_INDEX,
  RANKING_BY_INDEX,
  SEARCH_FIELDS_BY_INDEX,
} from './algolia-constants'

type SearchIndices<T extends string> = {
  [index in T]: SearchIndex
}

/**
 * Initializes search indices using Algolia.
 *
 * This function initializes Algolia search indices based on the provided list of index names.
 * It requires the environment variables `ALGOLIA_APPLICATION_ID` and `ALGOLIA_API_KEY` to be set.
 *
 * @param {string[]} indices - An array of index names to initialize.
 * @returns {[Error|null, SearchIndices|null]} A tuple where the first element is an error (if any)
 * and the second element is the initialized search indices or null in case of an error.
 */
export function createSearchIndices<T extends string>(indices: T[]): [Error, null] | [null, SearchIndices<T>] {
  if (process.env.ALGOLIA_APPLICATION_ID && process.env.ALGOLIA_API_KEY) {
    try {
      const searchClient = algoliasearch(process.env.ALGOLIA_APPLICATION_ID, process.env.ALGOLIA_API_KEY)

      const searchIndices: Partial<SearchIndices<T>> = {}
      for (const index of indices) {
        const searchIndex = searchClient.initIndex(index)

        searchIndex
          .setSettings({
            searchableAttributes: SEARCH_FIELDS_BY_INDEX[index] || ['*'],
            attributesForFaceting: FACET_FIELDS_BY_INDEX[index] || [],
            numericAttributesForFiltering: NUMERIC_FIELDS_BY_INDEX[index] || [],
            customRanking: RANKING_BY_INDEX[index] || [],
          })
          .then()

        searchIndices[index] = searchIndex
      }

      return [null, searchIndices as SearchIndices<T>]
    } catch (e) {
      if (e instanceof Error) {
        return [e, null]
      } else {
        return [new Error(String(e)), null]
      }
    }
  } else {
    return [
      new Error(
        'Unable to init algolia search, `ALGOLIA_APPLICATION_ID` or `ALGOLIA_API_KEY` environment variables are not set',
      ),
      null,
    ]
  }
}
