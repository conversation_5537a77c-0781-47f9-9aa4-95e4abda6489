CREATE TABLE IF NOT EXISTS contractors (
    id TEXT PRIMARY KEY,
    brand_image_url TEXT,
    name TEXT NOT NULL
);

CREATE TABLE IF NOT EXISTS projects (
    id SERIAL PRIMARY KEY,
    project_status TEXT,
    project_address TEXT,
    scans <PERSON>SO<PERSON> NOT NULL,
    products JSON,
    designs JSO<PERSON>,
    contractor_id TEXT NOT NULL,
    FOREIGN KEY (contractor_id) REFERENCES contractors(id)
);
