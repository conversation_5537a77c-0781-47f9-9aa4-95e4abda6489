import axios from 'axios'

const ACCESS_CONTROL_BASE_URL = 'https://api.averyapi.com/access-control/v1'

export default class AccessControlClient {
  static #instance = axios.create({
    baseURL: ACCESS_CONTROL_BASE_URL,
    headers: {
      'Content-Type': 'application/json',
    },
  })

  static getUserPermissions = async (userId: string): Promise<UserPermissionsResponse> => {
    const res = await this.#instance.get<UserPermissionsResponse>(
      `/users/${userId}/permissions?filter[resource_pattern]=${encodeURIComponent('/v2/projects*')}`,
    )
    return res.data
  }

  static attachFullAccessPermissionToUser = async (userId: string, projectId: string): Promise<void> => {
    const permisisonPayload = {
      resource_pattern: `/v2/projects/${projectId}*`,
      allow_read: true,
      allow_create: true,
      allow_update: true,
      allow_delete: true,
    }

    const { data: createdPermission } = await this.#instance.post(`/permissions`, permisisonPayload)
    if (createdPermission) {
      await this.#instance.post(`/users/${userId}/permissions`, { permission_id: createdPermission.data.id })
    }
  }
}

type UserPermissionsResponse = {
  data: PermissionResponse[]
}

type PermissionResponse = {
  id: string
  user_id: string
  permission: {
    id: string
    resource_pattern: string
    permissions: number
  }
}
