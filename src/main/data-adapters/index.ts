import type { QueryResult } from 'pg'
import { cloneDeep } from 'lodash'
import pool from '../www'
import CamelCaseConverter from '../ts/CamelCaseConverter'

export type Pagination = {
  total: number
  lastPage: number
  to: number
  from: number
  currentPage: number
  perPage: number
}

export type PaginatedData<T> = {
  data: T[]
  pagination?: Pagination
}

export type ColumnTableMapping = string | Record<string, string>

export type WhereClauseConstructor = {
  whereClause: string
  parameters: any[]
}

export class PaginatedDataFetcher {
  tableName: string

  constructor(tableName: string) {
    this.tableName = tableName
  }

  async fetchWithPagination(
    baseQuery: string,
    filters: Record<string, any[]> = {},
    joinQuery: string | undefined = undefined,
    currentPage: number,
    perPage: number,
    columnTableMapping: ColumnTableMapping = {},
    sortBy?: string | undefined,
    orderBy?: string | undefined,
    customOrderPattern?: string,
    filterKeyNameResolver?: (key: string) => string,
  ): Promise<PaginatedData<any>> {
    try {
      const offset = (currentPage - 1) * perPage

      let paginatedQuery = `SELECT ${baseQuery} FROM ${this.tableName}`

      if (joinQuery) {
        paginatedQuery += ` ${joinQuery}`
      }

      const { whereClause, parameters } = this.buildWhereClause(filters, columnTableMapping, filterKeyNameResolver)
      if (whereClause) {
        paginatedQuery += ` WHERE ${whereClause}`
      }

      if (sortBy && orderBy) {
        paginatedQuery += this.buildOrderByClause(sortBy, orderBy, columnTableMapping, customOrderPattern)
      }

      paginatedQuery += ` OFFSET $${parameters.length + 1} LIMIT $${parameters.length + 2}`
      parameters.push(offset, perPage)

      const result: QueryResult<any> = await this.executeQuery(paginatedQuery, parameters)
      const total = await this.countTotal(filters, joinQuery, columnTableMapping, filterKeyNameResolver)

      const data = result.rows

      const pagination: Pagination = {
        total: total,
        lastPage: Math.ceil(total / perPage),
        to: offset + data.length,
        from: offset + 1,
        currentPage: Number(currentPage),
        perPage: Number(perPage),
      }

      return { data, pagination }
    } catch (error) {
      console.error('fetchWithPagination error:', error)
      throw error
    }
  }

  async fetchData(
    baseQuery: string,
    filters: Record<string, any[]> = {},
    joinQuery: string | undefined = undefined,
    columnTableMapping: ColumnTableMapping = {},
    sortBy?: string | undefined,
    orderBy?: string | undefined,
    customOrderPattern?: string,
    filterKeyNameResolver?: (key: string) => string,
  ): Promise<PaginatedData<any>> {
    try {
      let paginatedQuery = `SELECT ${baseQuery} FROM ${this.tableName}`

      if (joinQuery) {
        paginatedQuery += ` ${joinQuery}`
      }

      const { whereClause, parameters } = this.buildWhereClause(filters, columnTableMapping, filterKeyNameResolver)
      if (whereClause) {
        paginatedQuery += ` WHERE ${whereClause}`
      }

      if (sortBy && orderBy) {
        paginatedQuery += this.buildOrderByClause(sortBy, orderBy, columnTableMapping, customOrderPattern)
      }

      const result: QueryResult<any> = await this.executeQuery(paginatedQuery, parameters)

      const data = result.rows

      const pagination: Pagination = {
        total: data.length,
        lastPage: 1,
        to: data.length,
        from: 1,
        currentPage: 1,
        perPage: data.length,
      }

      return { data, pagination }
    } catch (error) {
      console.error('fetchData error:', error)
      throw error
    }
  }

  async executeFetch(
    baseQuery: string,
    filters: Record<string, any[]> = {},
    joinQuery: string | undefined = undefined,
    currentPage: number,
    perPage: number,
    columnTableMapping: ColumnTableMapping = {},
    sortBy?: string | undefined,
    orderBy?: string | undefined,
    customOrderPattern?: string,
    filterKeyNameResolver?: (key: string) => string,
  ): Promise<PaginatedData<any>> {
    const fetchAll = Boolean(currentPage === undefined) || isNaN(currentPage as number)
    if (fetchAll) {
      return await this.fetchData(
        baseQuery,
        filters,
        joinQuery,
        columnTableMapping,
        sortBy,
        orderBy,
        customOrderPattern,
        filterKeyNameResolver,
      )
    }
    return await this.fetchWithPagination(
      baseQuery,
      filters,
      joinQuery,
      currentPage,
      perPage,
      columnTableMapping,
      sortBy,
      orderBy,
      customOrderPattern,
      filterKeyNameResolver,
    )
  }

  private async executeQuery(query: string, values: any[] = []): Promise<QueryResult<any>> {
    try {
      return await pool.query(query, values)
    } catch (error) {
      console.error('Database connection error:', error)
      throw error
    }
  }

  private async countTotal(
    filters: Record<string, any[]> = {},
    joinQuery: string | undefined = undefined,
    columnTableMapping: ColumnTableMapping = {},
    filterKeyNameResolver?: (key: string) => string,
  ): Promise<number> {
    try {
      let query = `SELECT count(*) as total FROM ${this.tableName}`
      const { whereClause, parameters } = this.buildWhereClause(filters, columnTableMapping, filterKeyNameResolver)
      if (joinQuery) {
        query += ` ${joinQuery}`
      }
      if (whereClause) {
        query += ` WHERE ${whereClause}`
      }

      const result = await this.executeQuery(query, parameters)
      return Number(result.rows[0].total)
    } catch (error) {
      console.error('countTotal error:', error)
      throw error
    }
  }

  public buildWhereClause(
    filters: Record<string, any[]>,
    columnTableMapping: ColumnTableMapping,
    filterKeyNameResolver?: (key: string) => string,
  ): WhereClauseConstructor {
    const clauses: string[] = []
    const parameters: any[] = []
    let paramIndex = 1

    for (const [key, values] of Object.entries(cloneDeep(filters))) {
      const keyName = filterKeyNameResolver ? filterKeyNameResolver(key) : key
      try {
        let condition: string
        if (keyName.includes('>')) {
          // Detect JSON fields
          const [jsonTable, jsonField] = key.split('>')

          if (this.validateDateRange(values)) {
            // Date range
            const [from, to] = this.getTimestampsForDateRange(values[0], values[1])
            values[0] = from
            values[1] = to
            condition = `(${jsonTable}->>'${jsonField}')::double precision BETWEEN $${paramIndex++} AND $${paramIndex++}`
          } else {
            condition = `${jsonTable}->>'${jsonField}' IN (${values.map(() => `$${paramIndex++}`).join(', ')})`
          }
        } else {
          // Regular fields
          const tablePrefix = this.getTablePrefix(key, columnTableMapping)

          if (this.validateDateRange(values)) {
            // Date range
            const [from, to] = this.getTimestampsForDateRange(values[0], values[1])
            values[0] = from
            values[1] = to
            condition = `${tablePrefix}${keyName} BETWEEN $${paramIndex++} AND $${paramIndex++}`
          } else {
            condition = `${tablePrefix}${keyName} IN (${values.map(() => `$${paramIndex++}`).join(', ')})`
          }
        }

        clauses.push(condition)
        parameters.push(...values)
      } catch (error) {
        console.error(`Error processing filter for key '${key}':`, error)
      }
    }

    return { whereClause: clauses.join(' AND '), parameters }
  }

  private buildOrderByClause(
    sortBy: string,
    orderBy: string,
    columnTableMapping: ColumnTableMapping,
    customOrderPattern?: string,
  ): string {
    const formattedSortBy = CamelCaseConverter.camelToSnakeCase(sortBy)
    const tablePrefix = this.getTablePrefix(formattedSortBy, columnTableMapping)
    const direction = orderBy.toUpperCase() === 'DESC' ? 'DESC' : 'ASC'

    // If a custom pattern is provided, use it; otherwise use the default
    const orderClause = customOrderPattern
      ? `${customOrderPattern} ${direction}`
      : `${tablePrefix}${formattedSortBy} ${direction}`

    return ` ORDER BY ${orderClause}`
  }

  private getTablePrefix(column: string, columnTableMapping: ColumnTableMapping): string {
    const mappedTable = typeof columnTableMapping === 'string' ? columnTableMapping : columnTableMapping[column]
    return mappedTable ? `${mappedTable}.` : ''
  }

  private validateDateRange(values: string[]): boolean {
    if (values.length !== 2) {
      return false
    }
    const regex = /\{\{\d+\}\}/
    return values.every((v) => regex.test(v))
  }

  private getTimestampsForDateRange(from: string, to: string): [string, string] {
    const regex = /^\{\{|\}\}$/g
    return [from.replace(regex, ''), to.replace(regex, '')]
  }
}
