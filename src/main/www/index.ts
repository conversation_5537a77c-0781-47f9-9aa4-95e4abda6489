import '../config/tracer' // must come before importing any instrumented module.

import dotenv from 'dotenv'
dotenv.config()

import express from 'express'
import cors from 'cors'
import morgan from 'morgan'
import { Pool } from 'pg'
import HealthController from '../ts/HealthController'

import v2Router from '../v2/routes'
import { createSearchIndices } from '../config/algolia'
import AlgoliaService from '../ts/AlgoliaService'
import { CONTRACTORS_SEARCH_INDEX, PROJECTS_SEARCH_INDEX } from '../config/algolia-constants'
import { initOpenai } from '../config/openai'
import { validateAccessMiddleware } from '@arc-studio-ai/access-control-middleware'
import { initializeDatabase } from '../config/database'

initOpenai()

const pool = new Pool({
  user: process.env.DB_USER,
  host: process.env.DB_HOST,
  database: process.env.DB_NAME,
  password: process.env.DB_PASSWORD,
  port: parseInt(process.env.DB_PORT || '5432'),
  ssl:
    process.env.IS_LOCAL === 'true'
      ? false
      : {
          rejectUnauthorized: false,
        },
})

// Initialize database and run migrations
initializeDatabase().catch((error) => {
  console.error('Failed to initialize database:', error)
  process.exit(1)
})

const [err, searchIndices] = createSearchIndices([PROJECTS_SEARCH_INDEX, CONTRACTORS_SEARCH_INDEX])
if (err) console.warn(err)

const app = express()
const PORT = process.env.PORT || 4000

app.use(cors())
app.use(morgan('dev'))
app.use(express.json({ limit: '50mb' }))

app.use(
  validateAccessMiddleware({
    bypassUnauthorized: true,
    tenants: [
      {
        issuer: 'https://sacred-thrush-57.clerk.accounts.dev',
        secretKey: process.env.INTERNAL_CLERK_SECRET_KEY,
      },
      {
        issuer: 'https://upward-cub-99.clerk.accounts.dev',
        secretKey: process.env.CUSTOMER_CLERK_SECRET_KEY,
      },
    ],
  }),
)

app.use('/', HealthController)

app.use('/v2', v2Router)

app.listen(PORT, async () => {
  console.log(`Server is running on port ${PORT}`)

  if (!err) {
    console.log('Populating search index...')
    await AlgoliaService.addProjectsToSearchIndex()
    await AlgoliaService.addContractorsToSearchIndex()
    console.log('Search index successfully populated ✔️')
  }
})

export { searchIndices }
export default pool
