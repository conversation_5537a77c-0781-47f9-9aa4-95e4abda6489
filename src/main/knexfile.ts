import path from 'path'
import dotenv from 'dotenv'
import type { Knex } from 'knex'

dotenv.config({
  path: path.join(__dirname, '..', '..', '.env'),
})

const dbConfig: Knex.Config = {
  client: 'pg',
  connection: {
    host: process.env.DB_HOST ?? '127.0.0.1',
    port: parseInt(process.env.DB_PORT || '5432'),
    database: process.env.DB_NAME ?? 'alpha-prototype',
    user: process.env.DB_USER ?? 'postgres',
    password: process.env.DB_PASSWORD ?? 'postgres',
    ssl: process.env.IS_LOCAL
      ? false
      : {
          rejectUnauthorized: false,
        },
  },
  migrations: {
    directory: path.join(__dirname, 'migrations'),
  },
  seeds: {
    directory: path.join(__dirname, 'seeds'),
    loadExtensions: ['.ts'],
    extension: 'ts',
  },
}

export default dbConfig
