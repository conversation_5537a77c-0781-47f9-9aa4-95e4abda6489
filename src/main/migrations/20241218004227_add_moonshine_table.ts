import type { Knex } from 'knex'

export async function up(knex: Knex): Promise<void> {
  await knex.schema.createTable('moonshine', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'))
    table.text('project_id').references('id').inTable('projects').notNullable().onDelete('CASCADE')
    table.text('hash').notNullable()
    table.text('file_id')
  })
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTableIfExists('moonshine')
}
