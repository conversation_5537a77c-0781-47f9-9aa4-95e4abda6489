import { Knex } from 'knex'

export async function up(knex: Knex): Promise<void> {
  await knex.schema.createTable('favorites', (table) => {
    table.uuid('id').primary()
    table.text('product_id').notNullable()
    table.text('project_id').notNullable()

    table.primary(['id'])
    table.unique(['project_id', 'product_id'])

    table.foreign('project_id').references('id').inTable('projects').onDelete('CASCADE')

    table.foreign('product_id').references('id').inTable('products').onDelete('CASCADE')

    table.timestamps(true, true) // Adds created_at and updated_at
  })
}

export async function down(knex: Knex): Promise<void> {
  // await knex.schema.dropTableIfExists('favorites');
}
