import { Knex } from 'knex'

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable('renders', (table) => {
    table.timestamp('created').defaultTo(knex.fn.now()).notNullable()
    table.timestamp('updated').defaultTo(knex.fn.now()).notNullable()
  })
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable('renders', (table) => {
    table.dropColumn('created')
    table.dropColumn('updated')
  })
}
