import type { Knex } from 'knex'
import { generateReadableId, initializeIdGeneratorCache } from '../utils/generateReadableId'

export async function up(knex: Knex): Promise<void> {
  // Step 1: Add `customer_id` column to the `projects` table
  await knex.schema.alterTable('projects', (table) => {
    table.string('customer_id').nullable().references('id').inTable('members').onDelete('SET NULL')
  })

  // Step 2: Fetch all projects with non-null `customer_first_name`
  const projects = await knex('projects')
    .select('id as project_id', 'customer_first_name', 'customer_last_name', 'customer_email', 'customer_phone')
    .whereNotNull('customer_first_name')

  // Step 3: Insert a new member for each project with customer data and assign `customer_id`
  await initializeIdGeneratorCache(knex)
  for (const project of projects) {
    const memberId = generateReadableId('MEM')
    let name = project.customer_first_name
    if (project.customer_last_name) {
      name += ` ${project.customer_last_name}`
    }
    await knex('members').insert({
      id: memberId,
      project_id: project.project_id,
      first_name: project.customer_first_name,
      last_name: project.customer_last_name,
      email: project.customer_email,
      phone: project.customer_phone,
      name, // Keeping to prevent breaking the API
    })

    // Assign the newly created member to `customer_id` in the `projects` table
    await knex('projects').where('id', project.project_id).update({
      customer_id: memberId,
    })
  }

  // Step 4: Remove the customer-related columns from the `projects` table
  // Leave for next migration to prevent breaking the API
  // await knex.schema.alterTable('projects', (table) => {
  //   table.dropColumn('customer_first_name')
  //   table.dropColumn('customer_last_name')
  //   table.dropColumn('customer_email')
  //   table.dropColumn('customer_phone')
  // })
}

export async function down(knex: Knex): Promise<void> {
  // Step 1: Re-add the customer-related columns to the `projects` table
  // Leave for next migration to prevent breaking the API
  // await knex.schema.alterTable('projects', (table) => {
  //   table.string('customer_first_name').nullable()
  //   table.string('customer_last_name').nullable()
  //   table.string('customer_email').nullable()
  //   table.string('customer_phone').nullable()
  // })

  // Step 2: Fetch all projects with `customer_id`
  const projects = await knex('projects').select('id as project_id', 'customer_id').whereNotNull('customer_id')

  // Step 3: Populate the `projects` table with the customer data
  for (const project of projects) {
    const member = await knex('members')
      .select('first_name', 'last_name', 'email', 'phone')
      .where('id', project.customer_id)
      .first()

    if (member) {
      await knex('projects').where('id', project.project_id).update({
        customer_first_name: member.first_name,
        customer_last_name: member.last_name,
        customer_email: member.email,
        customer_phone: member.phone,
      })
    }
  }

  // Step 4: Remove `customer_id` column from the `projects` table
  await knex.schema.alterTable('projects', (table) => {
    table.dropColumn('customer_id')
  })

  // Step 5: Optionally, delete members that were only created as part of this migration
  // Note: If other members are using the same IDs, this step should be skipped
  const memberIds = projects.map((project) => project.customer_id)
  await knex('members').whereIn('id', memberIds).delete()
}
