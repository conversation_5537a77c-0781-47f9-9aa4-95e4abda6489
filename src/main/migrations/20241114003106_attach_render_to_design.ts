import { Knex } from 'knex'

export async function up(knex: Knex): Promise<void> {
  // Add 'design_id' foreign key to 'renders' table
  await knex.schema.alterTable('renders', (table) => {
    table.string('design_id')
  })

  // Backfill the 'design_id' column based on the 'url' field
  const renders = await knex('renders').select('id', 'url')
  for (const render of renders) {
    const designId = render.url
      ?.split('/')
      .at(-1)
      .replace(/.jpg|.png/, '')
      .replace()

    if (designId) {
      await knex('renders').where({ id: render.id }).update({ design_id: designId })
    }
  }

  await knex.schema.alterTable('renders', (table) => {
    table.string('design_id').notNullable().alter()
  })
}

export async function down(knex: Knex): Promise<void> {
  // Remove 'design_id' foreign key from 'renders' table
  await knex.schema.alterTable('renders', (table) => {
    table.dropColumn('design_id')
  })
}
