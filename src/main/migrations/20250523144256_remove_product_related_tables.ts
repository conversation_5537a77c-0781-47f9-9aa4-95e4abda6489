import type { Knex } from 'knex'

export async function up(knex: Knex): Promise<void> {
  return knex.schema
    .dropTableIfExists('specifications')
    .dropTableIfExists('specification_groups')
    .dropTableIfExists('product_highlights')
    .dropTableIfExists('favorites')
    .dropTableIfExists('products')
}

export async function down(_: Knex): Promise<void> {
  // No need to implement down migration for this case
}
