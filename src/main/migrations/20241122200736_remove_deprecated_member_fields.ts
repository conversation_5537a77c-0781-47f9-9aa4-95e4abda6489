import type { Knex } from 'knex'

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable('members', (table) => {
    table.dropColumn('name')
  })

  await knex.schema.alterTable('projects', (table) => {
    table.dropColumn('customer_first_name')
    table.dropColumn('customer_last_name')
    table.dropColumn('customer_email')
    table.dropColumn('customer_phone')
  })
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable('members', (table) => {
    table.string('name').nullable()
  })

  await knex.schema.alterTable('projects', (table) => {
    table.string('customer_first_name').nullable()
    table.string('customer_last_name').nullable()
    table.string('customer_email').nullable()
    table.string('customer_phone').nullable()
  })
}
