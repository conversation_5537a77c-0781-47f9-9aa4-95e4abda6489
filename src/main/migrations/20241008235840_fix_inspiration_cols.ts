import type { Knex } from 'knex'

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable('inspiration_images', (table) => {
    table.dropColumn('description')
    table.jsonb('contents').notNullable()
  })
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable('inspiration_images', (table) => {
    table.dropColumn('contents')
    table.text('description')
  })
}
