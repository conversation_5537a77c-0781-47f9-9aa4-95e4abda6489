import type { Knex } from 'knex'

export async function up(knex: Knex): Promise<void> {
  const hasColumn = await knex.schema.hasColumn('previews', 'render_ids')
  if (!hasColumn) {
    console.log("Adding 'render_ids' column to 'previews' table...")
    return knex.schema.alterTable('previews', function (table) {
      table.specificType('render_ids', 'UUID[]')
    })
  } else {
    console.log("'render_ids' column already exists in 'previews' table. Skipping.")
    return Promise.resolve()
  }
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.alterTable('previews', function (table) {
    table.dropColumn('render_ids')
  })
}
