import type { Knex } from 'knex'

// TODO: uncomment the following snippet to create the database schemas

export async function up(knex: Knex): Promise<void> {
  // await knex.schema.createTable('contractors', (table) => {
  //   table.text('id').primary()
  //   table.text('brand_image_url').nullable()
  //   table.text('name').notNullable()
  //   table.boolean('is_real').defaultTo(true).nullable()
  //   table.text('contact_name').nullable()
  //   table.text('contact_email').nullable()
  //   table.text('contact_phone').nullable()
  //   table.text('contact_address').nullable()
  // })
  //
  // await knex.schema.createTable('previews', (table) => {
  //   table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
  //   table.timestamp('updated').notNullable();
  //   table.timestamp('created').notNullable().defaultTo(knex.fn.now());
  //   table.integer('percentage_complete').notNullable().defaultTo(0);
  //   table.text('status').notNullable();
  //   table.text('project_id').nullable();
  // });
  //
  // await knex.schema.createTable('members', (table) => {
  //   table.text('id').primary()
  //   table.text('name').notNullable()
  //   table.text('email').nullable()
  //   table.string('phone', 20).nullable()
  //   table.text('project_id').notNullable()
  //   table.text('status').nullable()
  // })
  //
  // await knex.schema.createTable('products', (table) => {
  //   table.text('id').primary()
  //   table.json('details').nullable()
  // })
  //
  // await knex.schema.createTable('projects', (table) => {
  //   table.text('id').notNullable()
  //   table.text('app_status').nullable()
  //   table.text('address').nullable()
  //   table.json('scan').notNullable()
  //   table.json('products').nullable()
  //   table.json('designs').nullable()
  //   table.text('contractor_id').notNullable()
  //   table.json('measurements').nullable()
  //   table.text('name').nullable()
  //   table.json('materials').nullable()
  //   table.json('raw_scan').nullable()
  //   table.decimal('percentage_to_close').nullable()
  //   table.decimal('closing_month').nullable()
  //   table.json('notes').nullable()
  //   table.json('scan_notes').nullable()
  //   table.integer('number_of_scans').defaultTo(1).nullable()
  //   table.text('crm_status').nullable()
  //   table.integer('customer_price').defaultTo(0).nullable()
  //   table.integer('our_price').defaultTo(0).nullable()
  //   table.integer('margin_revenue').defaultTo(0).nullable()
  //   table.integer('margin_percentage').defaultTo(0).nullable()
  //   table.boolean('is_real').defaultTo(true).nullable()
  //   table.text('scan_success').nullable()
  //   table.text('crm_substatus').nullable()
  //   table.string('customer_first_name', 255).nullable()
  //   table.string('customer_last_name', 255).nullable()
  // })
  //
  // await knex.schema.createTable('render_tags', (table) => {
  //   table.uuid('render_id').notNullable();
  //   table.uuid('tag_id').notNullable();
  // });
  //
  // await knex.schema.createTable('renders', (table) => {
  //   table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
  //   table.integer('price').nullable();
  //   table.integer('skus').nullable();
  //   table.uuid('preview_id').nullable();
  //   table.json('layout').notNullable();
  //   table.text('description').nullable();
  //   table.text('url').nullable();
  //   table.text('title').notNullable();
  //   table.text('designer').notNullable();
  //   table.text('designer_image').nullable();
  // });
  //
  // await knex.schema.createTable('tags', (table) => {
  //   table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
  //   table.text('name').notNullable();
  // });
  //
  // await knex.schema.createTable('templates', (table) => {
  //   table.text('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
  //   table.jsonb('materials').nullable();
  //   table.integer('render_priority').nullable();
  //   table.text('color_scheme').nullable();
  //   table.text('name').nullable();
  //   table.text('description').nullable();
  //   table.text('atmosphere').nullable();
  //   table.text('color_palette').nullable();
  //   table.text('material_palette').nullable();
  //   table.text('plumbing_brand').nullable();
  //   table.text('lighting_brand').nullable();
  //   table.text('vanity_brand').nullable();
  //   table.text('toilet_brand').nullable();
  //   table.text('vanity_storage').nullable();
  //   table.text('inspiration').nullable();
  //   table.text('style').nullable();
  // });
}

export async function down(knex: Knex): Promise<void> {
  // await knex.schema.dropTable('contractors')
  // await knex.schema.dropTable('previews')
  // await knex.schema.dropTable('members')
  // await knex.schema.dropTable('products')
  // await knex.schema.dropTable('projects')
  // await knex.schema.dropTable('render_tags')
  // await knex.schema.dropTable('renders')
  // await knex.schema.dropTable('tags')
  // await knex.schema.dropTable('templates')
}
