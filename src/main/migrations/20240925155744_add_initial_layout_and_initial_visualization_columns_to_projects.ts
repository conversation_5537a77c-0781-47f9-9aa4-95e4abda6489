import type { Knex } from 'knex'

export async function up(knex: Knex): Promise<void> {
  return knex.schema.alterTable('projects', function (table) {
    table.string('initial_layout', 255)
    table.string('initial_visualization', 255)
  })
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.alterTable('projects', function (table) {
    table.dropColumn('initial_layout')
    table.dropColumn('initial_visualization')
  })
}
