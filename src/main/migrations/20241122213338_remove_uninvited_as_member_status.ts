import type { Knex } from 'knex'

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable('members', (table) => {
    table.string('status').nullable().alter()
  })

  await knex('members').update({ status: null }).where('status', '=', 'Uninvited')
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable('members', (table) => {
    table.string('status').notNullable().alter
  })

  await knex('members').update({ status: 'Uninvited' }).where('status', '=', null)
}
