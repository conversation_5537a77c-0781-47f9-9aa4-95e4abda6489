import type { Knex } from 'knex'

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable('inspiration_images', (table) => {
    table.dropForeign(['project_id'])
  })

  await knex.schema.alterTable('inspiration_images', (table) => {
    table.foreign('project_id').references('id').inTable('projects').onDelete('SET NULL')

    table.jsonb('ai_description')
  })
}

export async function down(knex: Knex): Promise<void> {
  // Reverse the above changes.
  await knex.schema.alterTable('inspiration_images', (table) => {
    table.dropForeign(['project_id'])
    table.foreign('project_id').references('id').inTable('projects').onDelete('CASCADE')
    table.dropColumn('ai_description')
  })
}
