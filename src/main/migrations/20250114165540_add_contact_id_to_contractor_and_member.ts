import type { Knex } from 'knex'

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable('contractors', function (table) {
    table.string('open_phone_contact_id')
  })
  await knex.schema.alterTable('members', function (table) {
    table.string('open_phone_contact_id')
  })
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable('contractors', function (table) {
    table.dropColumn('open_phone_contact_id')
  })
  await knex.schema.alterTable('members', function (table) {
    table.dropColumn('open_phone_contact_id')
  })
}
