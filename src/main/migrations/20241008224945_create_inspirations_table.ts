import type { Knex } from 'knex'

export async function up(knex: Knex): Promise<void> {
  await knex.schema.createTable('inspiration_images', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'))
    table.text('project_id').references('id').inTable('projects').notNullable().onDelete('CASCADE')
    table.string('image_url').notNullable()
    table.text('description')
  })
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTableIfExists('inspiration_images')
}
