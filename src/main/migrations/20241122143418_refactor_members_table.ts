import type { Knex } from 'knex'

export async function up(knex: Knex): Promise<void> {
  // Create new columns to the members table
  await knex.schema.alterTable('members', (table) => {
    table.string('status', 20).defaultTo('Uninvited').alter()
    table.string('first_name', 50).nullable()
    table.string('last_name', 50).nullable()
    table.timestamp('created_at').defaultTo(knex.fn.now()).notNullable()
    table.timestamp('updated_at').defaultTo(knex.fn.now()).notNullable()
  })

  // Migrate data from `name` to `first_name` and `last_name`
  const members = await knex('members').select('id', 'name')

  for (const member of members) {
    if (member.name) {
      const [firstName, ...lastNameParts] = member.name.split(' ')
      const lastName = lastNameParts.length > 0 ? lastNameParts.join(' ') : null // Handle case with no last name
      await knex('members').where('id', member.id).update({ first_name: firstName, last_name: lastName })
    } else {
      throw new Error(
        `Member with ID ${member.id} has a null name, which violates the non-null constraint for first_name.`,
      )
    }
  }

  // Remove the old `name` column and enforce `first_name` as NOT NULL after migration
  await knex.schema.alterTable('members', (table) => {
    // table.dropColumn('name') // Leave for next migration to prevent breaking the API
    table.string('first_name').notNullable().alter()
  })
}

export async function down(knex: Knex): Promise<void> {
  // Leave for next migration to prevent breaking the API
  // await knex.schema.alterTable('members', (table) => {
  //   table.string('name').nullable()
  // })

  // Migrate data back to `name` from `first_name` and `last_name`
  const members = await knex('members').select('id', 'first_name', 'last_name')

  for (const member of members) {
    const name = [member.first_name, member.last_name].filter(Boolean).join(' ')
    await knex('members').where('id', member.id).update({ name })
  }

  // Remove `first_name`, `last_name`, and `is_customer` columns
  await knex.schema.alterTable('members', (table) => {
    table.dropColumn('first_name')
    table.dropColumn('last_name')
    table.dropColumn('created_at')
    table.dropColumn('updated_at')
  })
}
