/* eslint-disable @typescript-eslint/no-explicit-any */
import { Contractor, ContractorExpanded, Customer, MarginInfo, Project, ProjectExpanded } from '../../ts/models/Project'

export const buildProjectsResponse = (project: any) => {
  const withMargin =
    project.customerPrice !== undefined &&
    project.ourPrice !== undefined &&
    project.marginPercentage !== undefined &&
    project.marginRevenue !== undefined

  const withCustomer = !!project.customerFirstName && !!project.customerLastName

  return new Project({
    id: project.id,
    address: project.address,
    isReal: project.isReal,
    name: project.name,
    scannedDate: project.scannedDate,
    numberOfScans: project.numberOfScans,
    appStatus: project.appStatus,
    crmStatus: project.crmStatus,
    crmSubstatus: project.crmSubstatus,
    memberEmail: project.groupMembers?.[0]?.email,
    memberName: project.groupMembers?.[0]?.name,
    memberStatus: project.groupMembers?.[0]?.status,
    scanNotes: project.scanNotes,
    notes: project.notes,
    percentageToClose: project.percentageToClose,
    closingMonth: project.closingMonth,
    products: project.products,
    contractor: new Contractor({
      id: project.contractor?.id || project.contractorId,
      isReal: project.contractor?.isReal || project.contractorIsReal,
      name: project.contractor?.name || project.contractorName,
    }),
    ...(withMargin && {
      marginInfo: new MarginInfo({
        customerPrice: project.customerPrice,
        ourPrice: project.ourPrice,
        marginPercentage: project.marginPercentage,
        marginRevenue: project.marginRevenue,
      }),
    }),
    scanSuccess: project.scanSuccess,
    groupMembers: project.groupMembers,
    customer: withCustomer
      ? new Customer({
          firstName: project.customerFirstName,
          lastName: project.customerLastName,
          email: project.customerEmail,
          phone: project.customerPhone,
        })
      : null,
    initialLayout: project.initialLayout,
    initialVisualization: project.initialVisualization,
  })
}

export const buildProjectsExpandedResponse = (project: any) => {
  return new ProjectExpanded({
    ...buildProjectsResponse(project),
    contractor: new ContractorExpanded({
      id: project.contractor?.id || project.contractorId,
      isReal: project.contractor?.isReal || project.contractorIsReal,
      name: project.contractor?.name || project.contractorName,
      brandImageUrl: project.contractor.brandImageUrl,
      contactAddress: project.contractor.contactAddress,
      contactEmail: project.contractor.contactEmail,
      contactName: project.contractor.contactName,
      contactPhone: project.contractor.contactPhone,
    }),
    scan: project.scan,
    rawScan: project.rawScan,
    measurements: project.measurements,
    designs: project.designs,
    scanNotes: project.scanNotes,
    notes: project.notes,
    percentageToClose: project.percentageToClose,
    closingMonth: project.closingMonth,
    numberOfScans: project.numberOfScans,
    materials: project.materials,
    members: project.members,
    previews: project.previews,
    designQuiz: project.designQuiz,
  })
}
