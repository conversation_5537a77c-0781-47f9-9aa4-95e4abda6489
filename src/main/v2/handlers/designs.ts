import { randomUUID } from 'crypto'
import { Design } from '../../ts/models/Design'

export const buildCreateDesignRequest = (newDesign: unknown) => {
  const design = newDesign as Design

  return new Design({
    id: design.id || randomUUID(),
    faucet: design.faucet,
    floorTile: design.floorTile,
    lighting: design.lighting,
    lightingPlacement: design.lightingPlacement,
    mirror: design.mirror,
    paint: design.paint,
    shelves: design.shelves,
    showerFloorTile: design.showerFloorTile,
    showerGlass: design.showerGlass,
    showerSystem: design.showerSystem,
    showerWallTile: design.showerWallTile,
    toilet: design.toilet,
    tub: design.tub,
    tubDoor: design.tubDoor,
    tubFiller: design.tubFiller,
    vanity: design.vanity,
    wallpaper: design.wallpaper,
    wallpaperPlacement: design.wallpaperPlacement,
    wallTile: design.wallTile,
    wallTilePlacement: design.wallTilePlacement,

    leadTimeDays: design.leadTimeDays,
    skuCount: design.skuCount,
    tags: design.tags || 0,
    totalPrice: design.totalPrice,

    title: design.title,
    description: design.description,

    isNichesVisible: design.isNichesVisible,
    isShowerGlassVisible: design.isShowerGlassVisible,
    isTubDoorVisible: design.isTubDoorVisible,
  })
}

export const buildUpdateDesignRequest = (designId: string, updatedDesign: unknown) => {
  const design = updatedDesign as Design

  return new Design({
    id: designId,
    faucet: design.faucet,
    floorTile: design.floorTile,
    lighting: design.lighting,
    lightingPlacement: design.lightingPlacement,
    mirror: design.mirror,
    paint: design.paint,
    shelves: design.shelves,
    showerFloorTile: design.showerFloorTile,
    showerGlass: design.showerGlass,
    showerSystem: design.showerSystem,
    showerWallTile: design.showerWallTile,
    toilet: design.toilet,
    tub: design.tub,
    tubDoor: design.tubDoor,
    tubFiller: design.tubFiller,
    vanity: design.vanity,
    wallpaper: design.wallpaper,
    wallpaperPlacement: design.wallpaperPlacement,
    wallTile: design.wallTile,
    wallTilePlacement: design.wallTilePlacement,

    wallTilePattern: design.wallTilePattern,
    floorTilePattern: design.floorTilePattern,
    showerWallTilePattern: design.showerWallTilePattern,
    showerFloorTilePattern: design.showerFloorTilePattern,

    leadTimeDays: design.leadTimeDays,
    skuCount: design.skuCount,
    tags: design.tags,
    totalPrice: design.totalPrice,

    isNichesVisible: design.isNichesVisible,
    isShowerGlassVisible: design.isShowerGlassVisible,
    isTubDoorVisible: design.isTubDoorVisible,
  })
}
