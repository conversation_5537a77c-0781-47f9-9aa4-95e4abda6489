import { Contractor } from '../../ts/models/Project'
import { PaginatedData, Pagination } from '../../data-adapters'
import { ContractorsSummaryDto } from './ContractorsSummary.dto'

export class AllContractorsDto<T extends Contractor = Contractor> implements PaginatedData<T> {
  public data: T[]
  public pagination?: Pagination

  public summary?: ContractorsSummaryDto

  public constructor(data: T[], pagination?: Pagination, summary?: ContractorsSummaryDto) {
    this.data = data
    this.pagination = pagination
    this.summary = summary
  }

  public setSummary(summary: ContractorsSummaryDto) {
    this.summary = summary
  }
}
