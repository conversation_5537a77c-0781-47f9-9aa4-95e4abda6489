import { ScanSuccess, ProjectStatus } from '../../ts/models/Project'

export class ContractorsSummaryDto {
  public totalProjects: number
  public totalRevenue: number
  public totalOurPrice: number
  public totalCustomerPrice: number
  public activeProjects: number
  public inProgressProjects: number
  public scannedProjects: number
  public scanToPay: number
  public scanToPayPercentage: number
  public totalProjectCounts: {
    [key in ProjectStatus]: number
  }
  public scanSuccessProjectCounts: {
    [key in ScanSuccess]: number
  }
  public activeContractors: number
  public mtdContractors: number

  public constructor(
    totalProjects: number,
    totalRevenue: number,
    totalOurPrice: number,
    totalCustomerPrice: number,
    activeProjects: number,
    inProgressProjects: number,
    scannedProjects: number,
    scanToPay: number,
    scanToPayPercentage: number,
    totalScannedProjects: number,
    totalInProgressProjects: number,
    totalProjectCreatedProjects: number,
    totalLayoutCapturedProjects: number,
    totalLayoutApprovedProjects: number,
    totalHomeownerInvitedProjects: number,
    totalSurveyCompletedProjects: number,
    totalHDRendersSharedProjects: number,
    totalDesignKickoffProjects: number,
    totalDesignReviewProjects: number,
    totalPaidProjects: number,
    totalOrderedProjects: number,
    totalShippedProjects: number,
    totalDeliveredProjects: number,
    totalPausedProjects: number,
    totalLostProjects: number,
    scanSuccessNotSetProjects: number,
    scanSuccessSuccessProjects: number,
    scanSuccessTechnicalErrorProjects: number,
    scanSuccessUserErrorProjects: number,
    activeContractors: number,
    mtdContractors: number,
  ) {
    this.totalProjects = totalProjects
    this.totalRevenue = totalRevenue
    this.totalOurPrice = totalOurPrice
    this.totalCustomerPrice = totalCustomerPrice
    this.activeProjects = activeProjects
    this.inProgressProjects = inProgressProjects
    this.scannedProjects = scannedProjects
    this.scanToPay = scanToPay
    this.scanToPayPercentage = scanToPayPercentage

    this.totalProjectCounts = {
      [ProjectStatus.Scanned]: totalScannedProjects,
      [ProjectStatus.InProgress]: totalInProgressProjects,
      [ProjectStatus.ProjectCreated]: totalProjectCreatedProjects,
      [ProjectStatus.LayoutCaptured]: totalLayoutCapturedProjects,
      [ProjectStatus.LayoutApproved]: totalLayoutApprovedProjects,
      [ProjectStatus.HomeownerInvited]: totalHomeownerInvitedProjects,
      [ProjectStatus.SurveyCompleted]: totalSurveyCompletedProjects,
      [ProjectStatus.HDRendersShared]: totalHDRendersSharedProjects,
      [ProjectStatus.DesignKickoff]: totalDesignKickoffProjects,
      [ProjectStatus.DesignReview]: totalDesignReviewProjects,
      [ProjectStatus.Paid]: totalPaidProjects,
      [ProjectStatus.Ordered]: totalOrderedProjects,
      [ProjectStatus.Shipped]: totalShippedProjects,
      [ProjectStatus.Delivered]: totalDeliveredProjects,
      [ProjectStatus.Paused]: totalPausedProjects,
      [ProjectStatus.Lost]: totalLostProjects,
    }

    this.scanSuccessProjectCounts = {
      [ScanSuccess.NotSet]: scanSuccessNotSetProjects,
      [ScanSuccess.Success]: scanSuccessSuccessProjects,
      [ScanSuccess.TechnicalError]: scanSuccessTechnicalErrorProjects,
      [ScanSuccess.UserError]: scanSuccessUserErrorProjects,
    }

    this.activeContractors = activeContractors
    this.mtdContractors = mtdContractors
  }
}
