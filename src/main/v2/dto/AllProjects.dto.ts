import { Project } from '../../ts/models/Project'
import { Pagination } from '../../data-adapters'
import { ProjectsSummaryDto } from './ProjectsSummary.dto'

export class AllProjectsDto {
  public data: Project[]
  public pagination?: Pagination

  public summary?: ProjectsSummaryDto

  public constructor(data: Project[], pagination?: Pagination, summary?: ProjectsSummaryDto) {
    this.data = data
    this.pagination = pagination
    this.summary = summary
  }

  public setSummary(summary: ProjectsSummaryDto) {
    this.summary = summary
  }
}
