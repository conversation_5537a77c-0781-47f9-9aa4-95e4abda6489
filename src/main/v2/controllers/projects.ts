import express, { Request } from 'express'
import { DesignQuizResult, DesignQuizResultStatus, ProjectService } from '../../ts/ProjectsService'
import OpenPhoneService from '../../ts/OpenPhoneService'
import { handleFilterQueryParams, parseParamIdString, parseInclude } from '../../utils/filterQueryParams'
import { buildProjectsExpandedResponse, buildProjectsResponse } from '../handlers/projects'
import AlgoliaService from '../../ts/AlgoliaService'
import CamelCaseConverter from '../../ts/CamelCaseConverter'
import { ProjectsSummaryDto } from '../dto'
import { AllProjectsDto } from '../dto/AllProjects.dto'
import { createDesignQuizResult } from '../../config/openai'
import { generateHash } from '../../utils/generateHash'
import AccessControlClient from '../../clients/accessControlClient'
import { validateResourcesMiddleware } from '@arc-studio-ai/access-control-middleware'

const router = express.Router()
const openPhoneService = new OpenPhoneService()
const projectService = new ProjectService()

router.get('/', validateResourcesMiddleware({ bypassUnauthorized: true }), async (req, res) => {
  try {
    const { currentPage, perPage, sortBy, orderBy, include, includeSummary, ...filterParams } = req.query

    const filters: Record<string, any[]> = handleFilterQueryParams(filterParams)
    const includeArray: string[] = parseInclude(include)

    const projects = await projectService.getAllProjects(
      perPage,
      currentPage,
      filters,
      orderBy as string,
      sortBy as string,
      includeArray,
    )

    const summary = includeSummary ? await projectService.getSummary(filters) : undefined

    if (!projects) {
      res.status(404).send('Projects not found')
      return
    }

    const allProjectsDto = new AllProjectsDto(projects.data.map(buildProjectsResponse), projects.pagination)

    if (summary) {
      allProjectsDto.setSummary(
        new ProjectsSummaryDto(
          summary.totalProjects,
          summary.totalRevenue,
          summary.totalOurPrice,
          summary.totalCustomerPrice,
          summary.activeProjects,
          summary.inProgressProjects,
          summary.scannedProjects,
          summary.scanToPay,
          summary.scanToPayPercentage,
          summary.totalScannedProjects,
          summary.totalInProgressProjects,
          summary.totalProjectCreatedProjects,
          summary.totalLayoutCapturedProjects,
          summary.totalLayoutApprovedProjects,
          summary.totalHomeownerInvitedProjects,
          summary.totalSurveyCompletedProjects,
          summary.totalHDRendersSharedProjects,
          summary.totalDesignKickoffProjects,
          summary.totalDesignReviewProjects,
          summary.totalPaidProjects,
          summary.totalOrderedProjects,
          summary.totalShippedProjectsMap,
          summary.totalDeliveredProjects,
          summary.totalPausedProjects,
          summary.totalLostProjects,
          summary.scanSuccessNotSetProjects,
          summary.scanSuccessSuccessProjects,
          summary.scanSuccessTechnicalErrorProjects,
          summary.scanSuccessUserErrorProjects,
        ),
      )
    }

    res.json(allProjectsDto)
    return
  } catch (error) {
    console.error('error fetching all the projects', error)
    res.status(500).send(`Error fetching projects: ${error}`)
  }
})

router.get('/summary', async (req, res) => {
  try {
    const summary = await projectService.getSummary()
    if (summary) {
      res.json(summary)
    } else {
      res.status(404).send('Summary not found')
    }
  } catch (error) {
    console.error('Error fetching summary:', error)
    res.status(500).send(`Failed to fetch summary ${error}`)
  }
})

router.get('/by-email/:email', async (req, res) => {
  const email = req.params.email

  try {
    const projects = await projectService.getProjectsByEmail(email)
    if (projects) {
      res.json(projects)
    } else {
      res.status(404).send('Projects not found')
    }
  } catch (error) {
    console.error('Error fetching projects:', error)
    res.status(500).send(`Failed to fetch projects ${error}`)
  }
})

router.get('/:id', async (req, res) => {
  const projectId = req.params.id
  const include = req.query.include
  const includeArray: string[] = parseInclude(include)
  const concatedIds = parseParamIdString(projectId)

  try {
    const project = await projectService.getProjectById(concatedIds, includeArray)
    if (project) {
      const projectResponse = { ...project, data: project.data.map(buildProjectsExpandedResponse) }
      res.json(projectResponse)
    } else {
      res.status(404).send('Project not found')
    }
  } catch (error) {
    console.error('Error fetching project:', error)
    res.status(500).send(`Failed to fetch project ${error}`)
  }
})

router.get('/:id/inspiration-images', async (req, res) => {
  const projectId = req.params.id
  try {
    const images = await projectService.getInspirationImagesForProject(projectId)

    if (images) {
      res.json({ data: images })
    } else {
      res.status(404).send('Inspiration images not found')
    }
  } catch (error) {
    res.status(500).send(`Error fetching inspiration images: ${error}`)
  }
})

router.post('/:id/send-slack-message', async (req, res) => {
  const projectId = req.params.id
  const message = req.body.message
  try {
    const result = await projectService.sendSlackMessage(projectId, message)
    res.status(200).send(result)
  } catch (error) {
    console.error('error:', error)
    res.status(500).send('Internal Server Error')
  }
})

router.post('/:id/inspiration-images', async (req, res) => {
  const projectId = req.params.id
  const imageData = req.body

  try {
    const image = await projectService.createInspirationImage(projectId, imageData)
    res.status(200).send(image)
  } catch (error) {
    console.error('error:', error)
    res.status(500).send('Internal Server Error')
  }
})

router.get('/:id/scan-artifacts', async (req, res) => {
  const projectId = req.params.id

  try {
    const scanArtifact = await projectService.getLatestScanArtifactsForProject(projectId)

    if (scanArtifact) {
      res.json({ data: [scanArtifact] })
    } else {
      res.status(404).send('Scan artifacts not found')
    }
  } catch (error) {
    res.status(500).send(`Error fetching scan artifacts: ${error}`)
  }
})

router.post('/:id/scan-artifacts', async (req, res) => {
  const projectId = req.params.id
  const scanDate = req.body.scanDate
  const rawScan = req.body.rawScan
  const arData = req.body.arData
  const video = req.body.video

  try {
    const scanArtifact = await projectService.createScanArtifactRecord(projectId, scanDate, rawScan, arData, video)
    res.status(200).send(scanArtifact)
  } catch (error) {
    console.error('error:', error)
    res.status(500).send('Internal Server Error')
  }
})

router.get('/:id/moonshine', async (req, res) => {
  const projectId = req.params.id
  const hash = req.query.hash as string | undefined

  try {
    const moonshineRecords = await projectService.getMoonshineRecordForProject(projectId, hash)

    if (moonshineRecords) {
      res.json({ data: moonshineRecords })
    } else {
      res.status(404).send('Moonshine record not found')
    }
  } catch (error) {
    res.status(500).send(`Error fetching moonshine record: ${error}`)
  }
})

router.post('/:id/moonshine', async (req, res) => {
  const projectId = req.params.id
  const hash = req.body.hash
  const fileId = req.body.fileId

  try {
    const image = await projectService.createMoonshineRecord(projectId, hash, fileId)
    res.status(200).send(image)
  } catch (error) {
    console.error('error:', error)
    res.status(500).send('Internal Server Error')
  }
})

router.get('/:id/previews', async (req, res) => {
  const projectId = req.params.id
  try {
    const previews = await projectService.getPreviewsForProject(projectId)

    if (previews) {
      res.json({ data: [previews] })
    } else {
      res.status(404).send('Previews not found')
    }
  } catch (error) {
    res.status(500).send(`Error fetching previews: ${error}`)
  }
})

router.get('/:id/designs', (req, res) => {
  const projectId = req.params.id
  res.status(308).appendHeader('Location', `/projects/${projectId}/designs`).send('Permanently moved')
  return
})

router.get('/:id/design-quiz-result', async (req, res) => {
  const projectId = req.params.id
  try {
    const { data: [project] = [] } = (await projectService.getProjectById([projectId])) || {}
    const designQuizResult = project?.designQuizResult

    if (!designQuizResult) {
      res.status(404).send({ message: 'Design quiz result for project not found' })
      return
    }

    res.send({ data: [designQuizResult] })
  } catch (error) {
    res.status(500).send(`Error fetching design quiz result: ${error}`)
  }
})

router.get('/:id/messages', async (req, res) => {
  const projectId = req.params.id
  try {
    const messages = await openPhoneService.getProjectMessages(projectId)

    if (messages) {
      res.json({ data: messages })
    } else {
      res.status(404).send('Messages not found')
    }
  } catch (error) {
    res.status(500).send(`Error fetching messages: ${error}`)
  }
})

router.post('/:id/design-quiz-result', async (req, res) => {
  const projectId = req.params.id
  try {
    const { data: [project] = [] } = (await projectService.getProjectById([projectId])) || {}
    const designQuiz = project?.designQuiz
    const designQuizResult = project?.designQuizResult

    if (!designQuiz) {
      res.status(404).send({ message: 'Design quiz for project not found' })
      return
    }

    const designQuizHash = generateHash(designQuiz)
    if (designQuizHash === designQuizResult?.hash) {
      res.status(400).send({ message: 'Design quiz not changed, unable to re-generate result' })
      return
    }

    await projectService.updateProject(projectId, { designQuizResult: new DesignQuizResult(designQuizHash) })
    res.status(201).send({ message: 'Generating design quiz result' })

    createDesignQuizResult(designQuiz)
      .then((result) => {
        if (result?.content) {
          console.log('Design quiz result generation completed')
          return projectService.updateProject(projectId, {
            designQuizResult: new DesignQuizResult(designQuizHash, DesignQuizResultStatus.COMPLETED, result.content),
          })
        }
      })
      .catch((error) => {
        console.error('Error generating design quiz result:', error)
        return projectService.updateProject(projectId, {
          designQuizResult: new DesignQuizResult(designQuizHash, DesignQuizResultStatus.ERROR),
        })
      })
  } catch (error) {
    res.status(500).send(`Error generating design quiz result: ${error}`)
  }
})

router.patch('/:id/previews/:previewId/renders/:renderId', async (req, res) => {
  const projectId = req.params.id
  const previewId = req.params.previewId
  const renderId = req.params.renderId
  console.log(`Patching render ${renderId} in preview ${previewId} for project ${projectId}.`)
  const payload = req.body
  try {
    const result = await projectService.updatePreviewRender(projectId, previewId, renderId, payload)
    if (result) {
      res.json({ data: { message: 'Render successfully updated' } })
    } else {
      res.status(404).send('Render was not found')
    }
  } catch (error) {
    res.status(500).send(`Error patching render with id ${renderId}: ${error}`)
  }
})

router.post('/clone', async (req, res) => {
  const body = req.body
  const { sourceProjectId, targetProjectId, selectedDesignId, isDemo } = body

  try {
    const { clonedProject, design } = await projectService.cloneProject(
      sourceProjectId,
      targetProjectId,
      selectedDesignId,
      isDemo ?? false,
    )

    if (clonedProject) {
      if (design) {
        clonedProject.designs = [design]
      } else {
        clonedProject.designs = []
      }
      res.json(CamelCaseConverter.toCamelCase(clonedProject))
    } else {
      res.status(404).send('Failed to clone project')
    }
  } catch (error) {
    res.status(500).send(`Error cloning project: ${error}`)
  }
})

router.post('/', async (req: Request & { auth?: { userId: string } }, res) => {
  try {
    const project = await projectService.createProject(req.body)

    // Check if authenticated request
    const userId = req.auth?.userId
    if (userId) {
      try {
        await AccessControlClient.attachFullAccessPermissionToUser(userId, project.id)
      } catch (error) {
        console.error(error)
      }
    }

    await AlgoliaService.addProjectToSearchIndex(project)
    res.status(201).json(project)
  } catch (error) {
    console.error('Error fetching project:', error)
    res.status(500).send('Failed to create project')
  }
})

router.put('/:id', async (req, res) => {
  const projectId = req.params.id
  const updateData = req.body
  const userAgent = req.get('User-Agent')
  console.info(`User-Agent: ${userAgent}`)

  try {
    const updatedProject = await projectService.updateProject(projectId, updateData)

    if (updatedProject) {
      res.json(CamelCaseConverter.toCamelCase(updatedProject))

      const project = await projectService.getProjectById([projectId], ['margin_info'])
      if (project?.data[0]) {
        await AlgoliaService.addProjectToSearchIndex(buildProjectsResponse(project?.data[0]), true)
      }
    } else {
      res.status(404).send('Project not found')
    }
  } catch (error) {
    console.error('Database error:', error)
    res.status(500).send('Internal Server Error')
  }
})

router.post('/:id/issues', async (req, res) => {
  const projectId = req.params.id
  const issueData = req.body

  try {
    await projectService.createIssue(projectId, issueData)
    res.status(204).send()
  } catch (error) {
    console.error('error:', error)
    res.status(500).send('Internal Server Error')
  }
})

router.post('/:id/previews', async (req, res) => {
  const userAgent = req.get('User-Agent')
  console.info(`User-Agent: ${userAgent}`)
  const projectId = req.params.id
  if (!Array.isArray(req.body)) {
    console.error(
      `Invalid request body. Expected array of design IDs; got ${typeof req.body}: ${JSON.stringify(req.body)}.`,
    )
    res.status(400).send('Invalid request body. Expected array of design IDs.')
    return
  }
  const designs = req.body
  console.log(`Creating preview for project ${projectId} with ${designs.length} designs.`)

  try {
    const shouldAppendRenderings = await projectService.checkForExistingPreview(projectId)

    if (shouldAppendRenderings) {
      console.log(`Updating existing preview for project ${projectId}`)
      await projectService.updatePreview(projectId, designs)
    } else {
      console.log(`Creating new preview for project ${projectId}`)
      await projectService.createPreview(projectId, designs)
    }
    res.status(204).send()
  } catch (error) {
    console.error('error:', error)
    res.status(500).send('Internal Server Error')
  }
})

router.post('/:id/members', async (req, res) => {
  const projectId = req.params.id
  const memberData = req.body

  // Validate input data (name, and at least email or phone should be provided)
  if (!memberData.name || (!memberData.email && !memberData.phone)) {
    res.status(400).send('Invalid member data')
    return
  }

  try {
    const newMember = await projectService.addMemberToProject(projectId, memberData)
    // openPhoneService.createContactMember(newMember)
    // openPhoneService.textMemberInvitation(newMember, projectId)
    res.status(201).json(newMember)
  } catch (error) {
    console.error('Error adding member to project:', error)
    res.status(500).send({ message: error instanceof Error ? error.message : error })
  }
})

router.post('/:id/designs', async (req, res) => {
  const projectId = req.params.id
  res.status(308).appendHeader('Location', `/projects/${projectId}/designs`).send('Permanently moved')
  return
})

router.put('/:id/members/:memberId', async (req, res) => {
  const memberId = req.params.memberId
  const updateData = req.body

  try {
    const updatedMember = await projectService.updateMember(memberId, updateData)
    if (updatedMember) {
      res.json(updatedMember)
    } else {
      res.status(404).send('Member not found')
    }
  } catch (error) {
    console.error('Error updating member', error)
    res.status(500).send('Internal Server Error')
  }
})

router.put('/:id/designs/:designId', async (req, res) => {
  const projectId = req.params.id
  const designId = req.params.designId
  res.status(308).appendHeader('Location', `/projects/${projectId}/designs/${designId}`).send('Permanently moved')
  return
})

router.delete('/:id', async (req, res) => {
  const projectId = req.params.id

  try {
    await projectService.deleteProject(projectId)
    await AlgoliaService.removeProjectFromSearchIndex(projectId)
    res.status(204).send() // No content to return
  } catch (error) {
    console.error('Error in deleting project:', error)
    res.status(500).send('Internal Server Error')
  }
})

router.delete('/:id/members/:memberId', async (req, res) => {
  const memberId = req.params.memberId

  try {
    await projectService.deleteMember(memberId)
    res.status(204).send() // No content to return
  } catch (error) {
    console.error('Error in deleting project:', error)
    res.status(500).send('Internal Server Error')
  }
})

router.delete('/:id/designs/:designId', async (req, res) => {
  const projectId = req.params.id
  const designId = req.params.designId
  res.status(308).appendHeader('Location', `/projects/${projectId}/designs/${designId}`).send('Permanently moved')
  return
})

router.delete('/:id/previews/:previewId/renders/:renderId', async (req, res) => {
  const projectId = req.params.id
  const previewId = req.params.previewId
  const renderId = req.params.renderId
  try {
    await projectService.deletePreviewRender(projectId, previewId, renderId)
    res.status(204).send() // No content to return
  } catch (error) {
    res.status(500).send(`Error deleting render with id ${renderId}: ${error}`)
  }
})

export default router
