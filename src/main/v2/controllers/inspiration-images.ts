import express from 'express'
import InspirationImagesService from '../../ts/InspirationImagesService'
import { InspirationImage } from '../../ts/models/InspirationImage'

const {
  getInspirationImagesById,
  getInspirationImagesByUrl,
  createInspirationImage,
  updateInspirationImage,
  deleteInspirationImage,
  getInspirationImagesByHash,
} = InspirationImagesService

const router = express.Router()

router.get('/:id', async (req, res) => {
  const inspirationImageId = req.params.id
  try {
    const inspirationImage = await getInspirationImagesById(inspirationImageId)

    if (inspirationImage) {
      res.json({ data: [inspirationImage] })
    } else {
      res.status(404).send('Inspiration image not found')
    }
  } catch (error) {
    res.status(500).send(`Error fetching inspiration image: ${error}`)
  }
})

// A throw-away getter that is able to retrieve inspiration images by URL
// TODO: Delete this after kbis and build flow around ID
router.get('/by-encoded-url/:base64Url', async (req, res) => {
  try {
    const { base64Url } = req.params
    const decodedUrl = Buffer.from(base64Url, 'base64').toString('utf8')
    const images = await getInspirationImagesByUrl(decodedUrl)

    if (images && images.length > 0) {
      res.json({ data: images })
    } else {
      res.status(404).send(`No images found with URL = '${decodedUrl}'`)
    }
  } catch (error) {
    res.status(500).send(`Error retrieving images by encoded URL: ${error}`)
  }
})

router.get('/by-image-hash/:imageHash', async (req, res) => {
  try {
    const { imageHash } = req.params
    const image = await getInspirationImagesByHash(imageHash)

    if (image) {
      res.json({ data: [image] })
    } else {
      res.status(404).send(`No images found with hash = '${imageHash}'`)
    }
  } catch (error) {
    res.status(500).send(`Error retrieving images by hash: ${error}`)
  }
})

router.post('/', async (req, res) => {
  try {
    const newImage: InspirationImage = req.body

    const createdImage = await createInspirationImage(newImage)
    res.status(201).json({ data: [createdImage] })
  } catch (error) {
    res.status(500).send(`Error creating inspiration image: ${error}`)
  }
})

router.put('/:id', async (req, res) => {
  const imageId = req.params.id
  try {
    const updatedImage = await updateInspirationImage(imageId, req.body)
    if (!updatedImage) {
      res.status(404).send('Inspiration image not found or no fields updated')
    } else {
      res.json({ data: [updatedImage] })
    }
  } catch (error) {
    res.status(500).send(`Error updating inspiration image: ${error}`)
  }
})

router.delete('/:id', async (req, res) => {
  const imageId = req.params.id
  try {
    const deletedImage = await deleteInspirationImage(imageId)
    if (!deletedImage) {
      res.status(404).send('Inspiration image not found')
    } else {
      res.json({ data: [deletedImage] })
    }
  } catch (error) {
    res.status(500).send(`Error deleting inspiration image: ${error}`)
  }
})

export default router
