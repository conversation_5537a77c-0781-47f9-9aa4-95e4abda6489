import express from 'express'
import { ContractorsService } from '../../ts/ContractorsService'
import OpenPhoneService from '../../ts/OpenPhoneService'
import { handleFilterQueryParams, parseParamIdString } from '../../utils/filterQueryParams'
import AlgoliaService from '../../ts/AlgoliaService'
import { AllContractorsDto } from '../dto/AllContractors.dto'
import { ContractorsSummaryDto } from '../dto/ContractorsSummary.dto'

const openPhoneService = new OpenPhoneService()

const router = express.Router()
const contractorsService = new ContractorsService()

const TRUE = 'true'

router.get('/', async (req, res) => {
  try {
    const { currentPage, perPage, sortBy, orderBy, includeSummary, ...filterQueryParams } = req.query
    const filters: Record<string, any[]> = handleFilterQueryParams(filterQueryParams)

    const result = await contractorsService.getAllContractors(
      currentPage,
      perPage,
      filters,
      orderBy as string,
      sortBy as string,
    )

    const allContractorsDto = new AllContractorsDto(result.data, result.pagination)

    if (String(includeSummary).toLowerCase() === TRUE) {
      const summary = await contractorsService.getSummary(filters)
      allContractorsDto.setSummary(
        new ContractorsSummaryDto(
          summary.totalProjects,
          summary.totalRevenue,
          summary.totalOurPrice,
          summary.totalCustomerPrice,
          summary.activeProjects,
          summary.inProgressProjects,
          summary.scannedProjects,
          summary.scanToPay,
          summary.scanToPayPercentage,
          summary.totalScannedProjects,
          summary.totalInProgressProjects,
          summary.totalProjectCreatedProjects,
          summary.totalLayoutCapturedProjects,
          summary.totalLayoutApprovedProjects,
          summary.totalHomeownerInvitedProjects,
          summary.totalSurveyCompletedProjects,
          summary.totalHDRendersSharedProjects,
          summary.totalDesignKickoffProjects,
          summary.totalDesignReviewProjects,
          summary.totalPaidProjects,
          summary.totalOrderedProjects,
          summary.totalShippedProjectsMap,
          summary.totalDeliveredProjects,
          summary.totalPausedProjects,
          summary.totalLostProjects,
          summary.scanSuccessNotSetProjects,
          summary.scanSuccessSuccessProjects,
          summary.scanSuccessTechnicalErrorProjects,
          summary.scanSuccessUserErrorProjects,
          summary.activeContractors,
          summary.mtdContractors,
        ),
      )
    }

    if (result) {
      res.json(allContractorsDto)
      return
    } else {
      res.status(404).send('Projects not found')
    }
  } catch (error) {
    console.error('Error fetching contractors:', error)
    res.status(500).send({ message: 'Error fetching contractors:', error })
  }
})

router.get('/:id', async (req, res) => {
  const contractorId = req.params.id.toUpperCase()
  const concatenatedId = parseParamIdString(contractorId)
  try {
    const contractor = await contractorsService.getContractorById(concatenatedId)
    if (contractor) {
      res.json(contractor)
    } else {
      res.status(404).send('Contractor not found')
    }
  } catch (error) {
    console.error('Error fetching contractor:', error)
    res.status(500).send(`Error fetching contractor: ${JSON.stringify(error)}`)
  }
})

router.post('/', async (req, res) => {
  const contractorData = req.body
  try {
    const newContractor = await contractorsService.createNewContractor(contractorData)
    if (newContractor) {
      AlgoliaService.addContractorToSearchIndex(newContractor)
      openPhoneService.upsertContactContractor(newContractor)
      res.json('Contractor created')
    } else {
      res.status(409).send('Contractor already exists')
    }
  } catch (error) {
    console.error('Error creating contractor:', error)
    res.status(500).send(`Error creating contractor: ${error}`)
  }
})

router.patch('/:id', async (req, res) => {
  const contractorId = req.params.id.toUpperCase()
  const contractorData = req.body
  try {
    const updatedContractor = await contractorsService.patchContractor(contractorId, contractorData)
    if (updatedContractor) {
      res.json(updatedContractor)

      const result = await contractorsService.getContractorById([contractorId])
      if (result?.data[0]) {
        AlgoliaService.addContractorToSearchIndex(result.data[0])
        openPhoneService.upsertContactContractor(updatedContractor)
      }
    } else {
      res.status(404).send('Contractor not found')
    }
  } catch (error) {
    console.error('Error updating contractor:', error)
    res.status(500).send(`Error updating contractor: ${error}`)
  }
})

router.delete('/:id', async (req, res) => {
  const contractorId = req.params.id.toUpperCase()
  try {
    const deletedContractor = await contractorsService.deleteContractor(contractorId)
    if (deletedContractor) {
      AlgoliaService.removeContractorFromSearchIndex(contractorId)
      res.json('Contractor deleted')
    } else {
      res.status(404).send('Contractor not found')
    }
  } catch (error: any) {
    console.error('Error deleting contractor:', error)
    res.status(500).send(`Error deleting contractor: ${error}`)
  }
})

router.get('/:id/messages', async (req, res) => {
  const contractorId = req.params.id
  try {
    const messages = await openPhoneService.getContractorMessages(contractorId)
    res.status(200).send(messages)
  } catch (error) {
    res.status(500).send(`Error fetching messages: ${error}`)
  }
})

export default router
