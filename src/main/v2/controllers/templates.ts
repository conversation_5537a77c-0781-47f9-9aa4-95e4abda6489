import express from 'express'
import TemplateService from '../../ts/TemplateService'

const { getAllTemplates, getTemplateById, updateTemplate, createNewTemplate, deleteTemplate } = TemplateService

const router = express.Router()

router.get('/:id', async (req, res) => {
  const templateId = req.params.id
  try {
    const template = await getTemplateById(templateId)

    if (template) {
      res.json({ data: [template] })
    } else {
      res.status(404).send('Template not found')
    }
  } catch (error) {
    res.status(500).send(`Error fetching template: ${error}`)
  }
})

router.get('/', async (req, res) => {
  try {
    const limit = req.query.limit ? parseInt(req.query.limit as string) : 10
    const offset = req.query.offset ? parseInt(req.query.offset as string) : 0
    const templates = await getAllTemplates(limit, offset)
    res.json({ data: templates })
  } catch (error) {
    res.status(500).send(`Error fetching templates: ${error}`)
  }
})

router.post('/', async (req, res) => {
  const templateData = req.body
  try {
    const newTemplate = await createNewTemplate(templateData)
    if (newTemplate) {
      res.status(201).json('Template created')
    } else {
      res.status(409).send('Contractor already exists')
    }
  } catch (error) {
    res.status(500).send(`Error creating template: ${error}`)
  }
})

router.put('/:id', async (req, res) => {
  const templateId = req.params.id
  const templateData = req.body
  try {
    const updatedTemplate = await updateTemplate(templateId, templateData)
    if (updatedTemplate) {
      res.json(updatedTemplate)
    } else {
      res.status(404).send('Template not found')
    }
  } catch (error) {
    res.status(500).send(`Error updating template: ${error}`)
  }
})

router.delete('/:id', async (req, res) => {
  const templateId = req.params.id
  try {
    const deletedTemplate = await deleteTemplate(templateId)
    if (deletedTemplate) {
      res.json('Template deleted')
    } else {
      res.status(404).send('Template not found')
    }
  } catch (error) {
    res.status(500).send(`Error deleting template: ${error}`)
  }
})

export default router
