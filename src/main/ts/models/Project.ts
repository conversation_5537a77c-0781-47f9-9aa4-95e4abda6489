export class Project {
  id: string
  appStatus: string
  crmStatus: string
  crmSubstatus?: CRMSubstatusEnum
  name: string
  address: string
  scannedDate: Date
  isReal: boolean
  numberOfScans?: number | string
  scanNotes?: object | string | null
  notes?: object | string | null
  percentageToClose?: number | string
  closingMonth?: number | string

  memberEmail?: string | null
  memberName?: string | null
  memberStatus?: string | null
  marginInfo?: MarginInfo

  scanSuccess?: ScanSuccess

  contractor: Contractor
  products: object //TODO: Make this strong typed
  groupMembers?: object[] | null
  customer?: Customer | null

  initialLayout?: string | null
  initialVisualization?: string | null

  constructor(newProject: Project) {
    this.id = newProject.id
    this.appStatus = newProject.appStatus
    this.crmStatus = newProject.crmStatus
    this.crmSubstatus = newProject.crmSubstatus
    this.name = newProject.name
    this.address = newProject.address
    this.scannedDate = newProject.scannedDate
    this.isReal = newProject.isReal
    this.numberOfScans = newProject.numberOfScans
    this.scanNotes = newProject.scanNotes
    this.notes = newProject.notes
    this.percentageToClose = newProject.percentageToClose
    this.closingMonth = newProject.closingMonth
    this.scanSuccess = newProject.scanSuccess
    this.groupMembers = newProject.groupMembers

    this.memberEmail = newProject.memberEmail
    this.memberName = newProject.memberName
    this.memberStatus = newProject.memberStatus

    this.marginInfo = newProject.marginInfo
    this.contractor = newProject.contractor
    this.products = newProject.products
    this.customer = newProject.customer

    this.initialLayout = newProject.initialLayout
    this.initialVisualization = newProject.initialVisualization
  }
}

export class ProjectExpanded extends Project {
  scan: object
  rawScan: object
  measurements: object
  designs: object
  scanNotes?: object | string | null
  notes?: object | string | null
  percentageToClose?: number | string
  closingMonth?: number | string
  numberOfScans?: number | string
  materials?: Material[] | null
  members: object[] | null
  scanSuccess?: ScanSuccess
  previews: object | null
  designQuiz?: object | string | null

  constructor(newProjectExpanded: ProjectExpanded) {
    const {
      scan,
      rawScan,
      measurements,
      designs,
      scanNotes,
      notes,
      percentageToClose,
      closingMonth,
      numberOfScans,
      materials,
      members,
      scanSuccess,
      previews,
      designQuiz,
      ...newProject
    } = newProjectExpanded
    super(newProject)

    this.scan = scan
    this.rawScan = rawScan
    this.measurements = measurements
    this.designs = designs
    this.scanNotes = scanNotes
    this.notes = notes
    this.percentageToClose = percentageToClose
    this.closingMonth = closingMonth
    this.numberOfScans = numberOfScans
    this.materials = materials
    this.members = members
    this.scanSuccess = scanSuccess
    this.previews = previews
    this.designQuiz = designQuiz
  }
}

export class Contractor {
  id: string
  name: string
  isReal: boolean

  constructor(newContractor: Contractor) {
    this.id = newContractor.id
    this.name = newContractor.name
    this.isReal = newContractor.isReal
  }
}

export class Customer {
  firstName: string
  lastName: string
  email?: string
  phone?: string

  constructor(newCustomer: Customer) {
    this.firstName = newCustomer.firstName
    this.lastName = newCustomer.lastName
    this.email = newCustomer.email
    this.phone = newCustomer.phone
  }
}

export class ContractorExpanded extends Contractor {
  contactName: string
  contactEmail: string
  contactPhone: string
  contactAddress: string
  brandImageUrl: string

  constructor(newContractorExpanded: ContractorExpanded) {
    const { contactName, contactEmail, contactPhone, contactAddress, brandImageUrl, ...newContractor } =
      newContractorExpanded
    super(newContractor)

    this.contactName = contactName
    this.contactEmail = contactEmail
    this.contactPhone = contactPhone
    this.contactAddress = contactAddress
    this.brandImageUrl = brandImageUrl
  }
}

export class MarginInfo {
  customerPrice: number
  ourPrice: number
  marginRevenue: number
  marginPercentage: number

  constructor(newMarginInfo: MarginInfo) {
    this.customerPrice = newMarginInfo.customerPrice
    this.ourPrice = newMarginInfo.ourPrice
    this.marginRevenue = newMarginInfo.marginRevenue
    this.marginPercentage = newMarginInfo.marginPercentage
  }
}

export class Material {
  price: number
  availableFrom: object[]

  constructor(newMaterial: Material) {
    this.price = newMaterial.price
    this.availableFrom = newMaterial.availableFrom
  }
}

export enum ScanSuccess {
  NotSet = 'not_set',
  Success = 'success',
  TechnicalError = 'technical_error',
  UserError = 'user_error',
}

export enum CRMSubstatusEnum {
  ClientDiscussion = 'client_discussion',
  MaterialListSent = 'material_list_sent',
  MaterialListApproved = 'material_list_approved',
  InvoiceSent = 'invoice_sent',
}

export enum ProjectStatus {
  Scanned = 'Scanned',
  InProgress = 'InProgress',
  ProjectCreated = 'ProjectCreated',
  LayoutCaptured = 'LayoutCaptured',
  LayoutApproved = 'LayoutApproved',
  HomeownerInvited = 'HomeownerInvited',
  SurveyCompleted = 'SurveyCompleted',
  HDRendersShared = 'HDRendersShared',
  DesignKickoff = 'DesignKickoff',
  DesignReview = 'DesignReview',
  Paid = 'Paid',
  Ordered = 'Ordered',
  Shipped = 'Shipped',
  Delivered = 'Delivered',
  Paused = 'Paused',
  Lost = 'Lost',
}
