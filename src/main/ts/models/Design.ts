export class Design {
  id: string
  faucet?: string | null
  floorTile?: string | null
  lighting?: string | null
  lightingPlacement?: string | null
  mirror?: string | null
  paint?: string | null
  shelves?: string | null
  showerFloorTile?: string | null
  showerGlass?: string | null
  showerSystem?: string | null
  showerWallTile?: string | null
  toilet?: string | null
  tub?: string | null
  tubDoor?: string | null
  tubFiller?: string | null
  vanity?: string | null
  wallpaper?: string | null
  wallpaperPlacement?: string | null
  wallTile?: string | null
  wallTilePlacement?: string | null

  wallTilePattern?: string | null
  floorTilePattern?: string | null
  showerWallTilePattern?: string | null
  showerFloorTilePattern?: string | null

  leadTimeDays?: number | null
  skuCount?: number | null
  tags?: number | null
  totalPrice?: number | null

  title?: string | null
  description?: string | null

  isNichesVisible?: boolean | null
  isShowerGlassVisible?: boolean | null
  isTubDoorVisible?: boolean | null

  constructor(newDesign: Design) {
    this.id = newDesign.id
    if (newDesign.faucet !== undefined) this.faucet = newDesign.faucet
    if (newDesign.floorTile !== undefined) this.floorTile = newDesign.floorTile
    if (newDesign.lighting !== undefined) this.lighting = newDesign.lighting
    if (newDesign.lightingPlacement !== undefined) this.lightingPlacement = newDesign.lightingPlacement
    if (newDesign.mirror !== undefined) this.mirror = newDesign.mirror
    if (newDesign.paint !== undefined) this.paint = newDesign.paint
    if (newDesign.shelves !== undefined) this.shelves = newDesign.shelves
    if (newDesign.showerFloorTile !== undefined) this.showerFloorTile = newDesign.showerFloorTile
    if (newDesign.showerGlass !== undefined) this.showerGlass = newDesign.showerGlass
    if (newDesign.showerSystem !== undefined) this.showerSystem = newDesign.showerSystem
    if (newDesign.showerWallTile !== undefined) this.showerWallTile = newDesign.showerWallTile
    if (newDesign.toilet !== undefined) this.toilet = newDesign.toilet
    if (newDesign.tub !== undefined) this.tub = newDesign.tub
    if (newDesign.tubDoor !== undefined) this.tubDoor = newDesign.tubDoor

    if (newDesign.tubFiller !== undefined) this.tubFiller = newDesign.tubFiller
    if (newDesign.vanity !== undefined) this.vanity = newDesign.vanity
    if (newDesign.wallpaper !== undefined) this.wallpaper = newDesign.wallpaper
    if (newDesign.wallpaperPlacement !== undefined) this.wallpaperPlacement = newDesign.wallpaperPlacement
    if (newDesign.wallTile !== undefined) this.wallTile = newDesign.wallTile
    if (newDesign.wallTilePlacement !== undefined) this.wallTilePlacement = newDesign.wallTilePlacement

    if (newDesign.wallTilePattern) this.wallTilePattern = newDesign.wallTilePattern
    if (newDesign.floorTilePattern) this.floorTilePattern = newDesign.floorTilePattern
    if (newDesign.showerWallTilePattern) this.showerWallTilePattern = newDesign.showerWallTilePattern
    if (newDesign.showerFloorTilePattern) this.showerFloorTilePattern = newDesign.showerFloorTilePattern

    if (newDesign.leadTimeDays !== undefined) this.leadTimeDays = newDesign.leadTimeDays
    if (newDesign.skuCount !== undefined) this.skuCount = newDesign.skuCount
    if (newDesign.tags !== undefined) this.tags = newDesign.tags
    if (newDesign.totalPrice !== undefined) this.totalPrice = newDesign.totalPrice

    if (newDesign.title !== undefined) this.title = newDesign.title
    if (newDesign.description !== undefined) this.description = newDesign.description

    if (newDesign.isNichesVisible !== undefined) this.isNichesVisible = newDesign.isNichesVisible
    if (newDesign.isShowerGlassVisible !== undefined) this.isShowerGlassVisible = newDesign.isShowerGlassVisible
    if (newDesign.isTubDoorVisible !== undefined) this.isTubDoorVisible = newDesign.isTubDoorVisible
  }
}
