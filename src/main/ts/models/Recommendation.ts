export class Recommendation {
  id: string
  name: string
  style: string
  highlights: {
    text: string
    display: string
  }[]
  description: string
  faucet: string
  floorTile: string
  lighting: string
  mirror: string
  paint: string
  showerGlass: string
  showerSystem: string
  showerWallTile: string
  toilet: string
  vanity: string
  wallTile: string
  shelves: string
  tub: string
  imageURL: string

  constructor(data: Partial<Recommendation> = {}) {
    this.id = data.id || ''
    this.name = data.name || ''
    this.style = data.style || ''
    this.highlights = data.highlights || []
    this.description = data.description || ''
    this.faucet = data.faucet || ''
    this.floorTile = data.floorTile || ''
    this.lighting = data.lighting || ''
    this.mirror = data.mirror || ''
    this.paint = data.paint || ''
    this.showerGlass = data.showerGlass || ''
    this.showerSystem = data.showerSystem || ''
    this.showerWallTile = data.showerWallTile || ''
    this.toilet = data.toilet || ''
    this.vanity = data.vanity || ''
    this.wallTile = data.wallTile || ''
    this.shelves = data.shelves || ''
    this.tub = data.tub || ''
    this.imageURL = data.tub || ''
  }
}
