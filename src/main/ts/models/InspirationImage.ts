export type InspirationImage = {
  id: string
  projectId?: string
  imageUrl: string
  notes?: string
  contents?: InspirationImageContents
  aiDescription?: AIDescription
  imageHash?: string
}

export type AIDescription = {
  vanity?: string
  toilet?: string
  floorTile?: string
  showerFloorTile?: string
  showerWallTile?: string
  bathtub?: string
  showerSystem?: string
  faucet?: string
  lighting?: string
  mirror?: string
  wallTile?: string
  showerGlass?: string
  tubDoor?: string
  shelves?: string
  paint?: string
  wallpaper?: string
  description?: string
}

export type InspirationImageContents = {
  vanity: boolean
  toilet: boolean
  floorTile: boolean
  showerFloorTile: boolean
  showerWallTile: boolean
  bathtub: boolean
  showerSystem: boolean
  faucet: boolean
  lighting: boolean
  mirror: boolean
  wallTile: boolean
  showerGlass: boolean
  tubDoor: boolean
  shelves: boolean
  paint: boolean
  wallpaper: boolean
}
