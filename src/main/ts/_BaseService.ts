export class BaseService {
  protected formatFilterValues(rawFilters: Record<string, any[]>): Record<string, any[]> {
    return Object.entries(rawFilters).reduce<Record<string, any[]>>((acc, [key, values]) => {
      // mark date range values to parse it on data adapter layer
      const isDateKey = ['scan>scannedDate', 'created'].includes(key)
      if (isDateKey && values.length === 2 && !isNaN(+values[0]) && !isNaN(+values[1])) {
        return { ...acc, [key]: [`{{${values[0]}}}`, `{{${values[1]}}}`] }
      }
      return { ...acc, [key]: values }
    }, {})
  }

  protected transformResponseNumericValues<T extends object>(summary: T) {
    return Object.keys(summary).reduce((result: Record<string, any>, key) => {
      result[key] = Number(summary[key as keyof T])
      return result
    }, {})
  }
}
