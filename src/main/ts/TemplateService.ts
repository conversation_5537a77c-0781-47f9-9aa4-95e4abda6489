import pool from '../www/index'

class TemplateService {
  async getAllTemplates(limit: number, offset: number) {
    try {
      const allTemplateQuery = 'SELECT * FROM templates ORDER BY id LIMIT $1 OFFSET $2'
      const templateResults = await pool.query(allTemplateQuery, [limit, offset])

      return templateResults.rows
    } catch (error: any) {
      const errorMessage = error.detail
      console.error('Database error:', error)
      throw new Error(errorMessage)
    }
  }

  async getTemplateById(templateId: string) {
    try {
      const templateQuery = 'SELECT * FROM templates WHERE id = $1'
      const templateResult = await pool.query(templateQuery, [templateId])
      if (templateResult.rows.length === 0) {
        return null
      }
      const template = templateResult.rows[0]

      if (!template) {
        return null
      }

      return template
    } catch (error: any) {
      const errorMessage = error.detail
      console.error('Database error:', error)
      throw new Error(errorMessage)
    }
  }

  async createNewTemplate(templateData: TemplateData) {
    try {

      const highlightedBrandUrls = templateData.highlighted_brand_urls ? JSON.stringify(templateData.highlighted_brand_urls) : null;

      const newTemplateQuery = `INSERT INTO templates (id, style, color_scheme, name, description, atmosphere, color_palette, material_palette, inspiration, plumbing_brand, lighting_brand, vanity_brand, toilet_brand, vanity_storage, materials, render_priority, image_url, highlighted_brand_urls)
                                    VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18)
                                    RETURNING *;`

      const values = [
        templateData.id,
        templateData.style,
        templateData.color_scheme,
        templateData.name,
        templateData.description,
        templateData.atmosphere,
        templateData.color_palette,
        templateData.material_palette,
        templateData.inspiration,
        templateData.plumbing_brand,
        templateData.lighting_brand,
        templateData.vanity_brand,
        templateData.toilet_brand,
        templateData.vanity_storage,
        templateData.materials,
        templateData.render_priority || 1,
        templateData.image_url,
        highlightedBrandUrls,
      ]

      const newTemplateResult = await pool.query(newTemplateQuery, values)
      if (newTemplateResult.rows.length === 0) {
        return null
      }
      return newTemplateResult.rows[0]
    } catch (error: any) {
      const errorMessage = error.detail
      console.error('Database error:', error)
      throw new Error(errorMessage)
    }
  }

  async updateTemplate(templateId: string, templateData: TemplateData) {
    try {
      
      const highlightedBrandUrls = templateData.highlighted_brand_urls ? JSON.stringify(templateData.highlighted_brand_urls) : null;

      const updateTemplateQuery = `UPDATE templates
                                        SET style = $2, color_scheme = $3, name = $4, description = $5, atmosphere = $6, color_palette = $7, material_palette = $8, inspiration = $9, plumbing_brand = $10, lighting_brand = $11, vanity_brand = $12, toilet_brand = $13, vanity_storage = $14, materials = $15, render_priority = $16, image_url = $17, highlighted_brand_urls = $18
                                        WHERE id = $1
                                        RETURNING *;`

      const values = [
        templateData.id,
        templateData.style,
        templateData.color_scheme,
        templateData.name,
        templateData.description,
        templateData.atmosphere,
        templateData.color_palette,
        templateData.material_palette,
        templateData.inspiration,
        templateData.plumbing_brand,
        templateData.lighting_brand,
        templateData.vanity_brand,
        templateData.toilet_brand,
        templateData.vanity_storage,
        templateData.materials,
        templateData.render_priority,
        templateData.image_url,
        highlightedBrandUrls,
      ]

      const updatedTemplateResult = await pool.query(updateTemplateQuery, values)
      if (updatedTemplateResult.rows.length === 0) {
        return null
      }
      return updatedTemplateResult.rows[0]
    } catch (error: any) {
      const errorMessage = error.detail
      console.error('Database error:', error)
      throw new Error(errorMessage)
    }
  }

  async deleteTemplate(templateId: string) {
    try {
      const deleteTemplateQuery = 'DELETE FROM templates WHERE id = $1 RETURNING *'
      const deletedTemplateResult = await pool.query(deleteTemplateQuery, [templateId])
      if (deletedTemplateResult.rows.length === 0) {
        return null
      }
      return deletedTemplateResult.rows[0]
    } catch (error: any) {
      const errorMessage = error.detail
      console.error('Database error:', error)
      throw new Error(errorMessage)
    }
  }
}

export default new TemplateService()

interface TemplateData {
  id: string
  name: string
  description: string
  color_scheme?: string
  style?: string
  atmosphere?: string
  color_palette?: string
  material_palette?: string
  inspiration?: string
  plumbing_brand?: string
  lighting_brand?: string
  vanity_brand?: string
  toilet_brand?: string
  vanity_storage?: string
  materials?: object
  render_priority?: number
  image_url?: string
  highlighted_brand_urls?: string[]
}
