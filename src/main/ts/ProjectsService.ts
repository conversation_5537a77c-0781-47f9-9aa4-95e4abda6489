/* eslint-disable @typescript-eslint/no-explicit-any */
import pool from '../www/index'
import CamelCaseConverter from './CamelCaseConverter'
import sgMail from '@sendgrid/mail'
import twilio from 'twilio'
import axios from 'axios'
import { capitalize, snakeCase } from 'lodash'
import { v4 as uuidv4 } from 'uuid'

import { InternalSite } from '../constants/internal-site'
import { NumberManipulator } from '../utils/numbersManipulator'
import type { ColumnTableMapping, PaginatedData } from '../data-adapters'
import { PaginatedDataFetcher } from '../data-adapters'
import { projectsGetIncludes } from '../constants/projects-includes'
import { CRMSubstatusEnum, ProjectStatus, ScanSuccess } from './models/Project'
import { HttpMethods } from '../constants/http-methods'
import { PoolClient } from 'pg'
import { BaseService } from './_BaseService'
import { Design, Design as ProjectDesign } from './models/Design'
import { InspirationImage } from './models/InspirationImage'
import { generateReadableId } from '../utils/generateReadableId'
import { Moonshine } from './models/moonshine'
import { getAppleEpochTime } from '../utils/appleEpoch'
import { ScanArtifact } from './models/ScanArtifact'

const CDN_HOSTNAME = 'cdn.arcstudio.ai'
const RENDER_API_URL = 'https://api.averyapi.com/gateway/ssr/message'
const ENTERPRISE_NOTIFICATIONS_SLACK_WEBHOOK_URL =
  '*********************************************************************************'

export class ProjectService extends BaseService {
  paginateService: PaginatedDataFetcher
  columnTableMapping: ColumnTableMapping

  constructor() {
    super()
    this.paginateService = new PaginatedDataFetcher('projects')

    this.columnTableMapping = {
      id: 'projects',
      app_status: 'projects',
      crm_status: 'projects',
      crm_substatus: 'projects',
      address: 'projects',
      name: 'projects',
      materials: 'projects',
      contractor_id: 'projects',
      products: 'projects',
      scannedDate: 'projects',
      scanned_date: 'projects',
      is_project_real: 'projects',
      is_real: 'contractors',
    }
  }

  async getAllProjects(
    perPage?: number | any,
    currentPage?: number | any,
    filters?: Record<string, any[]>,
    orderBy: string = 'DESC',
    sortBy: string = 'scannedDate',
    include: string[] = [],
  ): Promise<PaginatedData<any>> {
    const baseFields = [
      'projects.id',
      'projects.app_status',
      'projects.address',
      'projects.name',
      'projects.materials',
      'projects.products',
      'projects.percentage_to_close',
      'projects.closing_month',
      'projects.notes',
      'projects.scan_notes',
      'projects.number_of_scans',
      'projects.crm_status',
      'projects.crm_substatus',
      'projects.is_real AS is_real',
      'projects.scan_success',
      'projects.initial_layout',
      'projects.initial_visualization',
      "scan->'scannedDate' AS scanned_date",
      'contractors.id AS contractor_id',
      'contractors.is_real AS contractor_is_real',
      'contractors.name AS contractor_name',
      'members.first_name AS customer_first_name',
      'members.last_name AS customer_last_name',
      'members.email AS customer_email',
      'members.phone AS customer_phone',
      `(
          SELECT json_agg(m)
          FROM (
              SELECT
                  m.id, CONCAT(m.first_name, ' ', m.last_name) as name, m.email, m.status
              FROM members m
              WHERE m.project_id = projects.id
          ) m
      ) AS group_members`,
    ]

    const baseQuery = this.getIncludesFields(include, baseFields)

    const joinQuery = `
      LEFT JOIN contractors ON projects.contractor_id = contractors.id
      LEFT JOIN members ON projects.customer_id = members.id
    `

    const customOrderPattern =
      sortBy === 'scannedDate' ? "to_timestamp((scan->>'scannedDate')::double precision)" : undefined

    const res: PaginatedData<ProjectInfo> = await this.paginateService.executeFetch(
      baseQuery,
      filters ? this.formatFilterValues(filters) : undefined,
      joinQuery,
      currentPage,
      perPage,
      this.columnTableMapping,
      sortBy,
      orderBy,
      customOrderPattern,
      ProjectService.getFilterNameResolver,
    )

    const response = res.data.map((project) => {
      return CamelCaseConverter.toCamelCase(project)
    })

    return {
      ...res,
      data: response,
    }
  }

  async getProjectsByEmail(email: string) {
    const memberProjectsQuery = `
      SELECT project_id as id
      FROM members
      WHERE members.email = $1
    `

    const contractorProjectsQuery = `
      SELECT projects.id
      FROM projects
      JOIN contractors ON projects.contractor_id = contractors.id
      WHERE contractors.contact_email = $1
    `

    const client = await pool.connect()
    try {
      const memberProjectsResult = await client.query(memberProjectsQuery, [email])
      const contractorProjectsResult = await client.query(contractorProjectsQuery, [email])

      const memberProjects = memberProjectsResult.rows.map((row) => ({ id: row.id, role: 'member' }))
      const contractorProjects = contractorProjectsResult.rows.map((row) => ({ id: row.id, role: 'contractor' }))
      const projects = [...memberProjects, ...contractorProjects]

      return projects
    } catch (error) {
      console.error('Database error:', error)
      throw error
    } finally {
      client.release()
    }
  }

  async getProjectById(projectIds: string[] | string, include: string[] = []) {
    if (!Array.isArray(projectIds)) {
      projectIds = [projectIds]
    }
    if (projectIds.length > 1) {
      console.log(`Fetching data for ${projectIds.length} projects: ${projectIds.join(', ')}`)
    }

    try {
      const baseFields = [
        'projects.app_status',
        'projects.address',
        'projects.scan',
        'projects.contractor_id',
        'projects.id',
        'projects.products',
        'projects.measurements',
        'projects.name',
        'projects.percentage_to_close',
        'projects.closing_month',
        'projects.notes',
        'projects.scan_notes',
        'projects.number_of_scans',
        'projects.crm_status',
        'projects.crm_substatus',
        'projects.is_real',
        'projects.scan_success',
        'projects.design_quiz',
        'projects.design_quiz_result',
        'projects.initial_layout',
        'projects.initial_visualization',
        'projects.active_preview_id',
        'members.first_name AS customer_first_name',
        'members.last_name AS customer_last_name',
        'members.email AS customer_email',
        'members.phone AS customer_phone',
      ]
      const baseQuery = this.getIncludesFields(include, baseFields)

      let previewsQuery = ''
      if (include.includes('previews')) {
        previewsQuery = `
        , (
          SELECT json_build_object(
            'active', (
              SELECT json_build_object(
                'status', p.status,
                'created', p.created,
                'updated', p.updated,
                'initial_percentage', p.initial_percentage,
                'percentage_complete', p.percentage_complete,
                'should_display_gc_info', p.should_display_gc_info,
                'renders', (
                  SELECT json_agg(json_build_object(
                    'id', r.id,
                    'designId', r.design_id,
                    'url', r.url,
                    'title', r.title,
                    'designer', r.designer,
                    'designerImage', r.designer_image,
                    'tags', (
                      SELECT json_agg(t.name)
                      FROM render_tags rt
                      JOIN tags t ON rt.tag_id = t.id
                      WHERE rt.render_id = r.id
                    ),
                    'description', r.description,
                    'price', r.price,
                    'skus', r.skus,
                    'status', r.status,
                    'created', r.created,
                    'updated', r.updated
                  ))
                  FROM renders r
                  WHERE r.id = ANY(p.render_ids)
                )
              )
              FROM previews p
              WHERE p.id = projects.active_preview_id
            ),
            'previous', (
              SELECT json_agg(json_build_object(
                'status', p.status,
                'created', p.created,
                'updated', p.updated,
                'initial_percentage', p.initial_percentage,
                'percentage_complete', p.percentage_complete,
                'should_display_gc_info', p.should_display_gc_info,
                'renders', (
                  SELECT json_agg(json_build_object(
                    'id', r.id,
                    'designId', r.design_id,
                    'url', r.url,
                    'title', r.title,
                    'designer', r.designer,
                    'designerImage', r.designer_image,
                    'tags', (
                      SELECT json_agg(t.name)
                      FROM render_tags rt
                      JOIN tags t ON rt.tag_id = t.id
                      WHERE rt.render_id = r.id
                    ),
                    'description', r.description,
                    'price', r.price,
                    'skus', r.skus,
                    'status', r.status,
                    'created', r.created,
                    'updated', r.updated
                  ))
                  FROM renders r
                  WHERE r.id = ANY(p.render_ids)
                )
              ))
              FROM previews p
              WHERE p.project_id = projects.id AND p.id != projects.active_preview_id
            )
          ) AS previews
        )
      `
      }

      const projectQuery = `
        ${baseQuery}
        ${previewsQuery},
        (
          SELECT json_agg(m)
          FROM (
            SELECT
              m.id, CONCAT(m.first_name, ' ', m.last_name) as name, m.email, m.phone, m.status
            FROM members m
            WHERE m.project_id = projects.id
          ) m
        ) AS members,
        (
          SELECT row_to_json(c)
          FROM (
            SELECT
              *
            FROM contractors c
            WHERE c.id = projects.contractor_id
          ) c
        ) AS contractor
      `

      const joinQuery = `
        LEFT JOIN members ON projects.customer_id = members.id
      `

      const filters = {
        'projects.id': projectIds as string[],
      }

      const projectResult: PaginatedData<ProjectInfo> = await this.paginateService.executeFetch(
        projectQuery,
        filters,
        joinQuery,
        1,
        projectIds.length,
      )
      if (projectResult.data.length === 0) {
        return null
      }

      const idList = projectIds.join(',')
      if (projectIds.length > 1) {
        console.log(`Fetched ${projectResult.data.length} projects: ${idList}`)
      }
      interface DesignInfo {
        data: Design[]
      }
      type DesignData = Record<string, DesignInfo>
      const url = `https://api.averyapi.com/projects/designs?ids=${idList}`
      const designData: DesignData = await axios.get<DesignData>(url).then((res) => res.data)
      const projects = projectResult.data.map((project: ProjectInfo) => {
        const projectId = project.id
        if (!projectId) {
          throw new Error('Project ID is missing')
        }
        const designs = designData[projectId]?.data || []
        project.designs = designs
        const p = CamelCaseConverter.toCamelCase<ProjectInfo>(project)
        const c = CamelCaseConverter.toCamelCase(project?.contractor)
        let m: MemberUpdateData[] = []
        if (project.members !== null && project?.members?.length) {
          m = project.members.map(CamelCaseConverter.toCamelCase)
        }

        let previews = null
        if (include.includes('previews') && project.previews) {
          const processPreview = (preview: any): Preview => {
            return {
              status: preview.status,
              created: preview.created,
              updated: preview.updated,
              percentageComplete: preview.percentage_complete || preview.initial_percentage,
              shouldDisplayGCInfo: preview.should_display_gc_info,
              renders: preview.renders?.map(CamelCaseConverter.toCamelCase) || [],
            }
          }

          previews = {
            active: project.previews.active ? processPreview(project.previews.active) : null,
            previous: project.previews.previous ? project.previews.previous.map(processPreview) : [],
          }
        }

        const processedProject = {
          ...p,
          contractor: c,
          members: m,
          previews: previews,
        }

        return processedProject
      })
      return {
        ...projectResult,
        data: projects,
      }
    } catch (error) {
      console.error(`Database error while fetching project(s): ${projectIds}`, error)
      throw error
    }
  }

  async createProject(projectData: ProjectData) {
    const {
      contractorId,
      address,
      percentageToClose,
      closingMonth,
      isReal = true,
      scanSuccess = 'not_set',
      crmSubstatus = null,
      initialLayout = null,
      initialVisualization = null,
      customerFirstName = null,
      customerLastName = null,
      customerEmail = null,
      customerPhone = null,
    } = projectData
    let {
      id,
      appStatus,
      name,
      scan,
      rawScan,
      measurements,
      products,
      materials,
      notes,
      scanNotes,
      designQuiz = null,
    } = projectData
    let crmStatus
    let customerPrice = 0
    let ourPrice = 0
    let marginRevenue = 0
    let marginPercentage = 0

    if (!contractorId) {
      throw new Error('Contractor ID is required for creating a new project.')
    }

    if (!id) {
      id = generateReadableId('PRJ')
    }

    if (!appStatus) {
      appStatus = 'Scanned'
    }

    if (!name) {
      name = this.generateDefaultProjectName()
    }

    const appStatusLower = appStatus.toLowerCase()
    if (appStatusLower === 'scanned') {
      crmStatus = appStatus
    } else {
      crmStatus = 'InProgress'
    }

    // Calculate materials-related fields
    if (projectData.materials) {
      const materialsList: Material[] = Array.isArray(projectData.materials) ? projectData.materials : []

      customerPrice = this.calculateCustomerPrice(materialsList)
      ourPrice = this.calculateOurPrice(materialsList)

      if (ourPrice > 0 && customerPrice) {
        marginRevenue = this.calculateMarginRevenue(customerPrice, ourPrice)
        marginPercentage = this.calculateMarginPercentage(customerPrice, marginRevenue)
      }
    }

    if (scan) scan = JSON.stringify(scan)
    if (rawScan) rawScan = JSON.stringify(rawScan)
    if (measurements) measurements = JSON.stringify(measurements)
    if (products) products = JSON.stringify(products)
    if (materials) materials = JSON.stringify(materials)
    if (notes) notes = JSON.stringify(notes)
    if (scanNotes) scanNotes = JSON.stringify(scanNotes)
    if (designQuiz) designQuiz = JSON.stringify(designQuiz)

    const query = `
      INSERT INTO projects (id, app_status, crm_status, name, address, scan, measurements, products, contractor_id, percentage_to_close, closing_month, notes, scan_notes, 
        customer_price, our_price, margin_revenue, margin_percentage, is_real, scan_success, crm_substatus, design_quiz, initial_layout, initial_visualization)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22, $23)
      RETURNING *;
    `
    const values = [
      id,
      appStatus,
      crmStatus,
      name,
      address,
      scan,
      measurements,
      products,
      contractorId,
      percentageToClose,
      closingMonth,
      notes,
      scanNotes,
      customerPrice,
      ourPrice,
      marginRevenue,
      marginPercentage,
      isReal,
      scanSuccess,
      crmSubstatus,
      designQuiz,
      initialLayout,
      initialVisualization,
    ]

    const client = await pool.connect()
    try {
      await client.query('BEGIN')

      const result = await client.query(query, values)
      const resultingData = result.rows[0]

      if (customerFirstName) {
        const memberId = generateReadableId('MEM')
        const newMemberQuery = `
          INSERT INTO members (id, project_id, first_name, last_name, email, phone)
          VALUES ($1, $2, $3, $4, $5, $6)
          RETURNING *
        `
        const newMemberValues = [memberId, id, customerFirstName, customerLastName, customerEmail, customerPhone]

        // Create new member with customer provided data
        const newMemberResult = await client.query(newMemberQuery, newMemberValues)

        // Update project to have customer_id of the newly created member
        const updateProjectMemberIdQuery = `
          UPDATE projects SET customer_id = $1 WHERE id = $2;
        `
        await client.query(updateProjectMemberIdQuery, [memberId, id])

        resultingData.members = newMemberResult.rows.map(CamelCaseConverter.toCamelCase)
        resultingData.customer = {
          firstName: customerFirstName,
          lastName: customerLastName,
          email: customerEmail,
          phone: customerPhone,
        }
      }

      await client.query('COMMIT')

      // Send slack notification
      const isRealContractor = await this.checkIfRealContractor(contractorId)
      const isEnterprise = contractorId === 'KOHLER' || contractorId === 'REECE'

      this.sendSlackNotificationV2(
        `A new scan, <${InternalSite.PROJECTS_URL}/${id}|${id}>, was done by <${InternalSite.CONTRACTORS_URL}/${contractorId}|${contractorId}>`,
        '',
        isRealContractor,
        isEnterprise,
        async (threadId) => {
          const updateThreadIdQuery = `
          UPDATE projects SET thread_id = $1 WHERE id = $2;
          `
          await client.query(updateThreadIdQuery, [threadId, id])
        },
      )

      delete resultingData.customer_id

      return CamelCaseConverter.toCamelCase(resultingData)
    } catch (error) {
      client.query('ROLLBACK')
      console.error('Database error while creating project:', error)
      throw error
    } finally {
      client.release()
    }
  }

  async cloneProject(
    sourceProjectId: string,
    targetProjectId: string,
    selectedDesignId: string,
    isDemo: boolean = false,
  ) {
    //FIXME: Temp guard to prevent source project from being other than DEFAULT
    if (sourceProjectId !== 'DEFAULT') {
      throw new Error('Source project must be DEFAULT')
    }

    const client = await pool.connect()
    try {
      await client.query('BEGIN')

      const getProjectQuery = `
        SELECT scan, measurements, active_preview_id
        FROM projects
        WHERE id = $1
      `
      const [sourceProjectResult, targetProjectResult] = await Promise.all([
        client.query(getProjectQuery, [sourceProjectId]),
        client.query(getProjectQuery, [targetProjectId]),
      ])
      const sourceProject = sourceProjectResult.rows[0]
      const targetProject = targetProjectResult.rows[0]
      if (!sourceProject) {
        throw new Error('Source project not found')
      }
      if (!targetProject) {
        throw new Error('Target project not found')
      }

      const targetScan = targetProject.scan

      if (targetScan) {
        throw new Error('Target scan already exists, clone must be used on an empty project')
      }

      if (!sourceProject.scan) {
        throw new Error('Source scan not found')
      }
      const url = `https://api.averyapi.com/projects/${sourceProjectId}/designs`
      const designs = await axios
        .get(url)
        .then((response) => {
          return response.data
        })
        .catch((err) => {
          throw new Error(`Error getting source designs: ${err}`)
        })
      sourceProject.designs = designs

      const scan = { ...sourceProject.scan, scannedDate: getAppleEpochTime() }
      let previewId
      let designId = '01'
      let selectedDesign
      if (selectedDesignId) {
        const originalDesign = sourceProject.designs.find((d: ProjectDesign) => d.id === selectedDesignId)
        if (!originalDesign) {
          throw new Error('Source selected design not found')
        }
        selectedDesign = { ...sourceProject.designs.find((d: ProjectDesign) => d.id === selectedDesignId) }
        designId = uuidv4()
        selectedDesign.id = designId

        const createPreviewQuery = `
          INSERT INTO previews (status, initial_percentage, percentage_complete, project_id)
            SELECT status, initial_percentage, percentage_complete, $2
            FROM previews
            WHERE id = $1
          RETURNING *;
        `

        const createPreviewResult = await client.query(createPreviewQuery, [
          sourceProject.active_preview_id,
          targetProjectId,
        ])
        previewId = createPreviewResult.rows[0].id

        const createRenderQuery = `
        WITH inserted_render AS (
            -- Step 1: Insert the new render and return all of its columns.
            INSERT INTO renders (url, title, designer, designer_image, description, price, skus, layout, status, notes, preview_id, design_id)
            SELECT url, title, designer, designer_image, description, price, skus, layout, status, notes, $3, $4
            FROM renders
            WHERE preview_id = $1 AND design_id = $2
            RETURNING *
        ),
        updated_preview AS (
            -- Step 2: Use the ID from the CTE above to update the preview's render_ids array.
            UPDATE previews
            SET render_ids = COALESCE(render_ids, '{}') || ARRAY(SELECT id FROM inserted_render)
            WHERE id = $3
            RETURNING id -- This RETURNING is for the CTE, its output isn't used in the final result.
        )
        -- Step 3: Fulfill the original request by returning the complete data for the newly created render.
        SELECT *
        FROM inserted_render;
        `

        // Add selected design to nascent project via design service.
        console.log(`Adding initial design to project ${targetProjectId}.`)
        await axios
          .post(`https://api.averyapi.com/projects/${targetProjectId}/designs`, selectedDesign)
          .catch((err) => {
            throw new Error(`Error adding initial design to new project: ${err}`)
          })

        await client.query(createRenderQuery, [sourceProject.active_preview_id, selectedDesignId, previewId, designId])
      }

      const updateProjectQuery = `
        UPDATE projects
        SET app_status = 'DesignsReady', products = $2, measurements = $3, scan = $4, active_preview_id = $5
        WHERE id = $1
        RETURNING *;
      `

      const products = {
        currentDesignId: designId,
        // If the scan is a demo, it should pretend like the clone API wasn't called, and therefore leave off this key.
        // This will be used to key if the UI should show the scan wasn't added vs pretending it was
        ...(isDemo ? {} : { sourceProjectId }),
      }

      const result = await client.query(updateProjectQuery, [
        targetProjectId,
        JSON.stringify(products),
        sourceProject.measurements,
        scan,
        previewId,
      ])
      await client.query('COMMIT')

      return { clonedProject: result.rows[0], design: selectedDesign }
    } catch (error) {
      client.query('ROLLBACK')
      console.error(`Database error while cloning project ${sourceProjectId} to ${targetProjectId}:`, error)
      throw error
    } finally {
      client.release()
    }
  }

  async createIssue(projectId: string, issueData: IssueData) {
    const contractor = await this.getContractorInfoByProjectId(projectId)
    const isRealContractor = Boolean(contractor?.isReal)
    const contractorId = contractor?.id ?? ''
    const isEnterprise = contractorId === 'KOHLER'

    this.sendSlackNotification(
      `<${InternalSite.CONTRACTORS_URL}/${contractorId}|${contractorId}> has filed an issue with project <${InternalSite.PROJECTS_URL}/${projectId}|${projectId}>. \n"${issueData.issueText}"`,
      isRealContractor,
      isEnterprise,
    )
  }

  async sendSlackMessage(projectId: string, message: string) {
    const client = await pool.connect()

    const queryString = `SELECT thread_id FROM projects WHERE id = $1`

    try {
      client.query('BEGIN')
      const queryResult = await client.query(queryString, [projectId])
      const threadId = queryResult.rows[0].thread_id
      await client.query('COMMIT')
      const contractor = await this.getContractorInfoByProjectId(projectId)
      const isRealContractor = Boolean(contractor?.isReal)
      const contractorId = contractor?.id ?? ''
      const isEnterprise = contractorId === 'KOHLER' || contractorId === 'REECE'
      return this.sendSlackNotificationV2(message, threadId, isRealContractor, isEnterprise)
    } catch (error) {
      client.query('ROLLBACK')
      throw error
    } finally {
      client.release()
    }
  }

  async getInspirationImagesForProject(projectId: string) {
    const client = await pool.connect()

    const queryString = `SELECT * FROM inspiration_images WHERE project_id = $1`

    try {
      client.query('BEGIN')
      const queryResult = await client.query(queryString, [projectId])
      const rows = queryResult.rows
      const inspirationImages: InspirationImage[] = rows.map((row) => CamelCaseConverter.toCamelCase(row))
      await client.query('COMMIT')
      return inspirationImages
    } catch (error) {
      client.query('ROLLBACK')
      throw error
    } finally {
      client.release()
    }
  }

  async createInspirationImage(projectId: string, image: InspirationImage) {
    if (!projectId || !image) {
      throw new Error('projectId and image are required')
    }
    const client = await pool.connect()

    try {
      await client.query('BEGIN')
      const imageQuery = `
        INSERT INTO inspiration_images (project_id, image_url, contents, image_hash)
        VALUES ($1, $2, $3, $4)
        RETURNING *;
      `
      const imageValues = [projectId, image.imageUrl, image.contents, image.imageHash]
      const imageResult = await client.query(imageQuery, imageValues)
      const imageRow = imageResult.rows[0]
      const inspirationImage: InspirationImage = CamelCaseConverter.toCamelCase(imageRow)
      await client.query('COMMIT')

      return inspirationImage
    } catch (error) {
      await client.query('ROLLBACK')
      throw error
    } finally {
      client.release()
    }
  }

  async getLatestScanArtifactsForProject(projectId: string) {
    const client = await pool.connect()

    const queryString = `SELECT *
          FROM scan_artifacts
          WHERE project_id = $1
          ORDER BY scan_date DESC NULLS LAST
          LIMIT 1;
          `
    try {
      client.query('BEGIN')
      const params = [projectId]
      const queryResult = await client.query(queryString, params)
      const rows = queryResult.rows
      const scanArtifact: ScanArtifact[] = rows.map((row) => CamelCaseConverter.toCamelCase(row))
      await client.query('COMMIT')
      return scanArtifact[0]
    } catch (error) {
      client.query('ROLLBACK')
      throw error
    } finally {
      client.release()
    }
  }

  async createScanArtifactRecord(
    projectId: string,
    scanDate?: string | number | null,
    rawScan?: string | null,
    arData?: string | null,
    video?: string | null,
  ) {
    if (!projectId) throw new Error('projectId is required')
    const ts = scanDate ? new Date(scanDate) : new Date()
    if (Number.isNaN(ts.getTime())) {
      const err = new Error('scanDate is not a valid ISO-8601 string') as any
      err.code = 'INVALID_DATE'
      throw err
    }

    const client = await pool.connect()
    try {
      await client.query('BEGIN')
      const { rows } = await client.query(
        `
        INSERT INTO scan_artifacts
          (project_id, scan_date, raw_scan, ar_data, video)
        VALUES ($1, $2, $3, $4, $5)
        RETURNING *;
        `,
        [projectId, ts, rawScan ?? null, arData ?? null, video ?? null],
      )
      await client.query('COMMIT')
      return CamelCaseConverter.toCamelCase(rows[0])
    } catch (e) {
      await client.query('ROLLBACK')
      throw e
    } finally {
      client.release()
    }
  }

  async getMoonshineRecordForProject(projectId: string, hash: string | undefined) {
    const client = await pool.connect()

    let queryString = `SELECT * FROM moonshine WHERE project_id = $1`
    if (hash) {
      queryString += ` AND hash = $2`
    }
    try {
      client.query('BEGIN')
      const params = hash ? [projectId, hash] : [projectId]
      const queryResult = await client.query(queryString, params)
      const rows = queryResult.rows
      const moonshine: Moonshine[] = rows.map((row) => CamelCaseConverter.toCamelCase(row))
      await client.query('COMMIT')
      return moonshine
    } catch (error) {
      client.query('ROLLBACK')
      throw error
    } finally {
      client.release()
    }
  }

  async createMoonshineRecord(projectId: string, hash: string, fileId: string) {
    if (!projectId || !hash || !fileId) {
      throw new Error('projectId, hash, fileId are required')
    }
    const client = await pool.connect()

    try {
      await client.query('BEGIN')
      const moonshineQuery = `
        INSERT INTO moonshine (project_id, hash, file_id)
        VALUES ($1, $2, $3)
        RETURNING *;
      `
      const moonshineValues = [projectId, hash, fileId]
      const moonshineResult = await client.query(moonshineQuery, moonshineValues)
      const moonshineRow = moonshineResult.rows[0]
      const moonshine: Moonshine = CamelCaseConverter.toCamelCase(moonshineRow)
      await client.query('COMMIT')

      return moonshine
    } catch (error) {
      await client.query('ROLLBACK')
      throw error
    } finally {
      client.release()
    }
  }

  async createPreview(projectId: string, designsToRender: string[]) {
    if (!projectId || !designsToRender || designsToRender.length === 0) {
      throw new Error('projectId and designsToRender are required')
    }

    if (!Array.isArray(designsToRender)) {
      throw new Error(`designsToRender must be an array, got ${typeof designsToRender}`)
    }

    const client = await pool.connect()

    try {
      await client.query('BEGIN')

      const projectQuery = `
        SELECT id, scan, contractor_id
        FROM projects
        WHERE id = $1
      `
      const projectResult = await client.query(projectQuery, [projectId])
      const project = projectResult.rows[0]
      if (!project) {
        throw new Error('Project not found')
      }
      const designs = await axios.get(`https://api.averyapi.com/projects/${projectId}/designs`).then((res) => res.data)
      if (!designs) {
        throw new Error('Designs not found')
      }
      project.designs = designs

      const previewId = uuidv4()
      const defaultStatus = 'Initializing HD preview'
      const initialPercentage = Math.floor(Math.random() * 6) + 10
      const shouldDisplayGcInfo = project.contractor_id !== 'PROD-CAMERON'
      const previewQuery = `
        INSERT INTO previews (id, status, updated, created, project_id, initial_percentage, should_display_gc_info)
        VALUES ($1, $2, NOW(), NOW(), $3, $4, $5)
        RETURNING *;
      `
      const previewValues = [previewId, defaultStatus, projectId, initialPercentage, shouldDisplayGcInfo]
      const previewResult = await client.query(previewQuery, previewValues)
      const preview = previewResult.rows[0]

      const updateProjectQuery = `
        UPDATE projects
        SET active_preview_id = $1
        WHERE id = $2;
      `
      await client.query(updateProjectQuery, [previewId, projectId])
      await Promise.all(designsToRender.map((designId) => this.createRender(project, previewId, designId, client)))

      await client.query('COMMIT')

      return preview
    } catch (error) {
      await client.query('ROLLBACK')
      throw error
    } finally {
      client.release()
    }
  }

  async updatePreview(projectId: string, designsToRender: string[]) {
    const client = await pool.connect()

    if (!projectId || !designsToRender || designsToRender.length === 0) {
      throw new Error('projectId and designsToRender are required')
    }

    if (!Array.isArray(designsToRender)) {
      throw new Error(`designsToRender must be an array, got ${typeof designsToRender}`)
    }

    const queryString = `
      SELECT projects.id, projects.scan, previews.id as preview_id, previews.initial_percentage
      FROM projects
      JOIN previews ON projects.active_preview_id = previews.id
      WHERE projects.id = $1
    `
    const updatePreviewQuery = `
      UPDATE previews
      SET status = $1, percentage_complete = $2
      WHERE true
      AND id = $3
      AND project_id = $4
    `

    try {
      client.query('BEGIN')
      const queryResult = await client.query(queryString, [projectId])
      const result = queryResult.rows[0]
      const url = `https://api.averyapi.com/projects/${projectId}/designs`
      console.log(`Fetching designs from ${url}.`)
      const designs = await axios.get(url).then((res) => res.data)
      if (!designs) {
        throw new Error(`Designs not found for project ${projectId}.`)
      } else if (!Array.isArray(designs)) {
        throw new Error(`Designs is not an array for project ${projectId}.`)
      } else if (designs.length === 0) {
        throw new Error(`Project ${projectId} has no designs.`)
      }
      result.designs = designs
      const previewId = result?.preview_id
      const initialPercentage = result?.initial_percentage ?? 0

      await client.query(updatePreviewQuery, ['Initializing HD Preview', initialPercentage, previewId, projectId])
      await Promise.all(designsToRender.map((designId) => this.createRender(result, previewId, designId, client)))

      client.query('COMMIT')
    } catch (error) {
      client.query('ROLLBACK')
      throw error
    } finally {
      client.release()
    }
  }

  async getPreviewsForProject(projectId: string): Promise<Previews | null> {
    const client = await pool.connect()

    try {
      await client.query('BEGIN')

      const projectQuery = `
        SELECT active_preview_id
        FROM projects
        WHERE id = $1
      `
      const projectResult = await client.query(projectQuery, [projectId])

      if (projectResult.rows.length === 0) {
        return null
      }

      const activePreviewId = projectResult.rows[0].active_preview_id

      const previewsQuery = `
        SELECT p.id, p.status, p.percentage_complete, p.initial_percentage, p.should_display_gc_info, p.created, p.updated,
          (SELECT json_agg(json_build_object(
            'id', r.id,
            'designId', r.design_id,
            'url', r.url,
            'title', r.title,
            'designer', r.designer,
            'designerImage', r.designer_image,
            'tags', (
              SELECT json_agg(t.name)
              FROM render_tags rt
              JOIN tags t ON rt.tag_id = t.id
              WHERE rt.render_id = r.id
            ),
            'description', r.description,
            'price', r.price,
            'skus', r.skus,
            'status', r.status,
            'created', r.created,
            'updated', r.updated
          ))
          FROM renders r
          WHERE r.id = ANY(p.render_ids)) AS renders
        FROM previews p
        WHERE p.project_id = $1
      `
      const previewsResult = await client.query(previewsQuery, [projectId])

      await client.query('COMMIT')

      if (previewsResult.rows.length === 0) {
        return null
      }

      const active = previewsResult.rows.find((p) => p.id === activePreviewId) || null
      const previous = previewsResult.rows.filter((p) => p.id !== activePreviewId)

      const processPreview = (p: any): Preview => {
        return {
          status: p.status,
          created: p.created,
          updated: p.updated,
          percentageComplete: Number(p.percentage_complete || p.initial_percentage),
          shouldDisplayGCInfo: p.should_display_gc_info,
          renders:
            (p.renders || []).map((r: any) => {
              return {
                ...r,
                actions: {
                  PATCH: `/v2/projects/${projectId}/previews/${p.id}/renders/${r.id}`,
                  DELETE: `/v2/projects/${projectId}/previews/${p.id}/renders/${r.id}`,
                },
              }
            }) || [],
        }
      }

      return {
        active: active ? processPreview(active) : null,
        previous: previous.map(processPreview),
      }
    } catch (error) {
      await client.query('ROLLBACK')
      console.error('Error fetching previews:', error)
      throw error
    } finally {
      client.release()
    }
  }

  async getDesignsForProject(_projectId: string) {
    console.log(`Refusing to return designs for project ${_projectId}.`)
    throw new Error('Designs should be fetched through the new service now.')
  }

  async getDesignForProjectById(_projectId: string, _designId: string) {
    console.log(`Refusing to return design ${_designId} for project ${_projectId}.`)
    throw new Error('Designs should be fetched through the new service now.')
  }

  private async createRender(project: any, previewId: string, designId: string, client: PoolClient) {
    console.log(`Creating render for design ${designId} in project ${project.id} with preview ${previewId}.`)
    if (!project.designs || !Array.isArray(project.designs) || project.designs.length === 0) {
      throw new Error('Project designs not found')
    }
    const design = project.designs.find((d: ProjectDesign) => d.id === designId)
    if (!design) {
      throw new Error(`Design with id ${designId} not found in project`)
    }

    const fetchRenderQuery = `
      SELECT id
      FROM renders
      WHERE design_id = $1
      AND id IN (
        SELECT UNNEST(render_ids)
        FROM previews
        WHERE id = $2
      );
      `

    const fetchRenderResult = await client.query(fetchRenderQuery, [designId, previewId])
    const existingRender = fetchRenderResult.rows[0]
    const existingRenderId = existingRender?.id

    const templateQuery = `
      SELECT name, description
      FROM templates
      WHERE id = $1;
    `
    const templateResult = await client.query(templateQuery, [designId])
    const template = templateResult.rows[0]
    const renderId = existingRenderId || uuidv4()
    // TODO (tony): use design service to mutate renders.
    const actions = {
      PATCH: `/v2/projects/${project.id}/previews/${previewId}/renders/${renderId}`,
      DELETE: `/v2/projects/${project.id}/previews/${previewId}/renders/${renderId}`,
    }

    const apiRequestBody = {
      uuid: uuidv4(),
      projectId: project.id,
      roomData: project.scan,
      imageFormat: 'jpg',
      showShowerGlass: design.isShowerGlassVisible,
      actions,
      design: {
        id: designId,
        productData: design,
      },
    }

    // Send render request to SQS queue
    await axios.post(RENDER_API_URL, apiRequestBody)
    const title = design.title || template?.name
    const description = design.description || template?.description

    const renderValues = [
      renderId,
      previewId,
      'Bond Studio',
      'https://cdn.arcstudio.ai/images/designers/bond-studio.jpg',
      design.totalPrice,
      design.skuCount,
      project.scan,
      designId,
    ]

    // Temporary hack, when creating new renders, it's possible these values are not passed, and the below query fails
    // TODO: This path should be handled more gracefully and generate a title and description based on the project
    // We also should consider how to know when to generate a new one, vs use what is passed in
    if (title) {
      renderValues.push(title)
      renderValues.push(description)
    }

    if (existingRenderId) {
      const updatePreviewsQuery = `
      UPDATE previews
      SET render_ids = array_append(render_ids, $2)
      WHERE id = $1;
      `
      await client.query(updatePreviewsQuery, [previewId, renderId])
      const updateRenderQuery = `
      UPDATE renders
      SET 
        preview_id = $2, 
        designer = $3, 
        designer_image = $4, 
        price = $5, 
        skus = $6, 
        layout = $7, 
        design_id = $8, 
        ${title ? 'title = $9, ' : ''}
        ${title ? 'description = $10, ' : ''}
        status = 'pending', 
        updated = CURRENT_TIMESTAMP
      WHERE id = $1;
      `
      await client.query(updateRenderQuery, renderValues)
      // Update and exit if there is an existing render for the design
      return
    }

    if (!title) {
      renderValues.push('')
      renderValues.push('')
    }
    const renderQuery = `
      WITH new_render AS (
        -- Step 1: Insert the new render with all its data, omitting the old preview_id.
        -- The ID for the new render is provided as the first parameter.
        INSERT INTO renders (id, preview_id, designer, designer_image, price, skus, layout, design_id, title, description)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
        RETURNING *
      ),
      updated_preview AS (
        -- Step 2: Add the new render's ID ($1) to the preview's array ($2).
        UPDATE previews
        SET render_ids = array_append(render_ids, $1)
        WHERE id = $2
      )
      -- Step 3: Fulfill the original request by returning the complete new render row.
      SELECT * FROM new_render;
    `

    await client.query(renderQuery, renderValues)

    if (template) {
      const tagsToAssign = []

      const colorSchemeTag = template.color_scheme === 'Bold' ? 'Bold Colors' : 'Neutral Colors'
      tagsToAssign.push(colorSchemeTag)

      tagsToAssign.push(template.style)
      tagsToAssign.push(template.plumbing_brand)
      tagsToAssign.push(template.lighting_brand)
      tagsToAssign.push(template.vanity_brand)
      tagsToAssign.push(template.toilet_brand)

      const tagQuery = `
        SELECT id, name
        FROM tags
        WHERE name = ANY($1);
      `
      const tagResult = await client.query(tagQuery, [tagsToAssign])

      for (const tag of tagResult.rows) {
        const renderTagQuery = `
          INSERT INTO render_tags (render_id, tag_id)
          VALUES ($1, $2);
        `
        await client.query(renderTagQuery, [renderId, tag.id])
      }
    }
  }

  async updatePreviewRender(projectId: string, previewId: string, renderId: string, payload: UpdatePreviewRender) {
    const client = await pool.connect()
    let columnsToUpdate = Object.keys(payload)
      .map((columnName, i) => `${snakeCase(columnName)} = $${i + 4}`)
      .join(',')
    if (columnsToUpdate.includes('url')) {
      const url = new URL(payload.url)
      url.hostname = CDN_HOSTNAME
      payload.url = url.toString()
    }
    if (columnsToUpdate != '') {
      columnsToUpdate += ', '
    }
    const columnValues = Object.values(payload)

    const updateRenderQuery = `
      UPDATE renders
        SET ${columnsToUpdate} updated = CURRENT_TIMESTAMP
        FROM previews
        WHERE renders.id = $1
          AND previews.id = $2
          AND renders.id = ANY(previews.render_ids)
          AND previews.project_id = $3
    `
    const updatePreviewQuery = `
        UPDATE previews
        SET status = $1, percentage_complete = $2, updated = CURRENT_TIMESTAMP,
          render_ids = array_append(render_ids, $5)
        WHERE id = $3
        AND project_id = $4
    `
    const getPreviewQuery = `
        SELECT p.id, p.percentage_complete, p.initial_percentage,
          (SELECT json_agg(json_build_object(
            'status', r.status
          ))
          FROM renders r
          WHERE r.id = ANY(p.render_ids)
          AND r.status != 'archived') AS renders
        FROM previews p
        WHERE p.id = $1
      `

    try {
      await client.query('BEGIN')
      const updatedRender = await client.query(updateRenderQuery, [renderId, previewId, projectId, ...columnValues])
      const previewResult = await client.query(getPreviewQuery, [previewId])
      const preview = previewResult.rows[0]
      const renders = preview?.renders || []

      const pendingRenders = renders.filter((r: any) => r.status === 'pending')
      const startedRenders = renders.filter((r: any) => r.status === 'started')
      const rendersInTerminalState = renders.filter((r: any) => r.status === 'completed' || r.status === 'outdated')

      const initialPercentage = Number(preview.initial_percentage)
      const totalRendersCount = renders.length
      const pendingRendersCount = pendingRenders.length
      const startedRendersCount = startedRenders.length
      const terminalRendersCount = rendersInTerminalState.length
      const newPercentageComplete = Math.min(
        Math.ceil(initialPercentage + (100 - initialPercentage) * (terminalRendersCount / totalRendersCount)),
        100,
      )

      let newStatus = preview.status
      if (pendingRendersCount < totalRendersCount) {
        newStatus = `Processing HD preview (${terminalRendersCount + startedRendersCount}/${totalRendersCount})`
      }
      if (terminalRendersCount === totalRendersCount) {
        newStatus = 'HD preview ready'
      }

      await client.query(updatePreviewQuery, [newStatus, newPercentageComplete, previewId, projectId, renderId])
      await client.query('COMMIT')
      console.log(`Update render successful with id ${renderId}:`, updatedRender.rowCount)
      return updatedRender
    } catch (error) {
      await client.query('ROLLBACK')
      console.error('Error updating render:', error)
      throw error
    } finally {
      client.release()
    }
  }

  async updateProject(projectId: string, updateData: ProjectUpdateData) {
    let preparedData = this.prepareProjectUpdateData(updateData)
    preparedData = CamelCaseConverter.toSnakeCase(preparedData)
    const scan: any = updateData?.scan

    //Doing a bit of a hack here, I only want this to notify slack if appStatus is updating
    //but since once the appStatus is updated, it sends it again everytime. The easiest approach
    //is just to ignore requests that are appStatus along with other things (like scan)
    const contractor = await this.getContractorInfoByProjectId(projectId)
    const isRealContractor = Boolean(contractor?.isReal)
    const contractorId = contractor?.id ?? ''
    const isEnterprise = contractorId === 'KOHLER'

    if (updateData.appStatus === 'ReadyToOrder' && !preparedData.scan) {
      this.sendSlackNotification(
        `Project, <${InternalSite.PROJECTS_URL}/${projectId}/${projectId}>, was finalized with <${InternalSite.CONTRACTORS_URL}/${contractorId}|${contractorId}>`,
        isRealContractor,
        isEnterprise,
      )
    }

    // Check if setting status to survey completed in order to send slack notification
    let isStatusChange = false
    const isSurveyCompleted = updateData.crmStatus === ProjectStatus.SurveyCompleted

    const client = await pool.connect()
    await client.query('BEGIN')

    if (isSurveyCompleted) {
      const fetchCrmStatusQuery = 'SELECT crm_status FROM projects WHERE id = $1'
      try {
        const { rows: [{ crm_status: crmStatus = null } = {}] = [] } = await client.query(fetchCrmStatusQuery, [
          projectId,
        ])
        isStatusChange = crmStatus !== updateData.crmStatus
      } catch (error) {
        client.query('ROLLBACK')
        client.release()
        throw error
      }
    }

    if (isStatusChange) {
      this.sendSlackNotification(
        `Survey completed for project <${InternalSite.PROJECTS_URL}/${projectId}|${projectId}> by <${InternalSite.CONTRACTORS_URL}/${contractorId}|${contractorId}>`,
        isRealContractor,
        isEnterprise,
      )
    }

    const isUpdatingMember =
      !!updateData.customerFirstName ||
      !!updateData.customerLastName ||
      !!updateData.customerEmail ||
      !!updateData.customerPhone

    // Assign field and value to change for query construction
    const fields = Object.keys(preparedData)
    const values = Object.values(preparedData)

    if (!fields.length && !isUpdatingMember) {
      client.release()
      throw new Error('No valid fields provided for update')
    }

    // First we want to make sure that the appStatus field comes in the body to be updated and then we compare to also update crm_status, if necessary.
    if (updateData.appStatus) {
      const appStatusLower = updateData.appStatus.toLowerCase()
      if (appStatusLower === 'scanned') {
        fields.push('crm_status')
        values.push(updateData.appStatus)
      } else if (appStatusLower === 'designsready') {
        fields.push('crm_status')
        values.push('InProgress')
      }
    }

    // Calculate materials-related fields and add them to the update query
    if (updateData.materials) {
      const materialsList: Material[] = Array.isArray(updateData.materials) ? updateData.materials : []

      const customerPrice = this.calculateCustomerPrice(materialsList)
      const ourPrice = this.calculateOurPrice(materialsList)
      let marginRevenue = 0
      let marginPercentage = 0

      if (ourPrice > 0 && customerPrice) {
        marginRevenue = this.calculateMarginRevenue(customerPrice, ourPrice)
        marginPercentage = this.calculateMarginPercentage(customerPrice, marginRevenue)
      }

      const materialFields = [
        { field: 'customer_price', value: customerPrice },
        { field: 'our_price', value: ourPrice },
        { field: 'margin_revenue', value: marginRevenue },
        { field: 'margin_percentage', value: marginPercentage },
      ]

      for (const { field, value } of materialFields) {
        fields.push(field)
        values.push(value)
      }
    }

    if (scan && typeof scan === 'object' && scan?.scannedDate) {
      try {
        const project = await client.query('SELECT scan, number_of_scans FROM projects WHERE id = $1', [projectId])
        const oldScanDate = project.rows[0].scan?.scannedDate
        const number_of_scans = project.rows[0].number_of_scans
        if (scan.scannedDate !== oldScanDate) {
          fields.push('number_of_scans')
          values.push(number_of_scans + 1)
        }
      } catch (error) {
        console.error(error, 'updating scan date')
      }
    }

    const setQuery = fields.map((field, index) => `${field} = $${index + 1}`).join(', ')
    values.push(projectId)

    const updateQuery = `UPDATE projects SET ${setQuery} WHERE id = $${values.length} RETURNING *`
    const fetchQuery = `SELECT * FROM projects WHERE id = $1;`

    try {
      const result = await client.query(values.length > 1 ? updateQuery : fetchQuery, values)
      const projectResult = result.rows[0]

      if (isUpdatingMember) {
        const memberFields = {
          firstName: updateData?.customerFirstName,
          lastName: updateData?.customerLastName,
          email: updateData?.customerEmail,
          phone: updateData?.customerPhone,
        }

        if (projectResult.customer_id) {
          const filteredEntries = Object.entries(memberFields).filter(([, value]) => value != null)

          if (filteredEntries.length) {
            const setClause = filteredEntries
              .map(([camelKey], idx) => `${snakeCase(camelKey)} = $${idx + 1}`)
              .join(', ')

            const values = filteredEntries.map(([, v]) => v)
            values.push(projectResult.customer_id)

            await client.query(`UPDATE members SET ${setClause} WHERE id = $${values.length}`, values)
          }
        } else {
          const memberId = generateReadableId('MEM')

          const fieldValues = [
            memberFields.firstName ?? null,
            memberFields.lastName ?? null,
            memberFields.email ?? null,
            memberFields.phone ?? null,
          ]

          await client.query(
            `INSERT INTO members (id, project_id, first_name, last_name, email, phone)
             VALUES ($1, $2, $3, $4, $5, $6)`,
            [memberId, projectId, ...fieldValues],
          )

          await client.query(`UPDATE projects SET customer_id = $1 WHERE id = $2`, [memberId, projectId])
        }
      }

      if (!projectResult) {
        throw new Error('Project not found')
      }

      if (updateData?.scan && projectResult.active_preview_id) {
        console.log(`Marking all renders outdated for project ${projectId}...`)
        await client.query(
          `UPDATE renders SET status = 'outdated' 
          WHERE status = 'completed' 
            AND id IN (SELECT UNNEST(render_ids) FROM previews WHERE id = $1)`,
          [projectResult.active_preview_id],
        )
      }

      client.query('COMMIT')
      return projectResult
    } catch (error) {
      client.query('ROLLBACK')
      throw error
    } finally {
      client.release()
    }
  }

  async deleteProject(projectId: string) {
    const client = await pool.connect()

    try {
      await client.query('BEGIN')

      // First, delete all members associated with the project
      await client.query('DELETE FROM members WHERE project_id = $1', [projectId])

      // Then, delete the project itself
      const result = await client.query('DELETE FROM projects WHERE id = $1', [projectId])

      if (result.rowCount === 0) {
        throw new Error('Project not found')
      }

      await client.query('COMMIT')
    } catch (error) {
      await client.query('ROLLBACK')
      console.error('Database error in deleteProject:', error)
      throw new Error('Internal Server Error')
    } finally {
      client.release()
    }
  }

  async addMemberToProject(
    projectId: string,
    memberData: { id?: string; name: string; email?: string; phone?: string; status?: string },
  ) {
    try {
      let { id, status } = memberData
      const { name, email, phone } = memberData

      if (!id) {
        id = generateReadableId('MEM')
      }

      if (!status) {
        status = 'InvitePending'
      }

      const [firstName, ...lastNameParts] = name.split(' ')
      const lastName = lastNameParts.length > 0 ? lastNameParts.join(' ') : null

      const query = `
                INSERT INTO members (id, first_name, last_name, email, phone, status, project_id)
                VALUES ($1, $2, $3, $4, $5, $6, $7)
                RETURNING id, CONCAT(first_name, ' ', last_name) as name, email, phone, status`
      const values = [id, firstName, lastName, email, phone, status, projectId]

      const result = await pool.query(query, values)

      const contractorInfo = await this.getContractorInfoByProjectId(projectId)
      const isEnterprise = contractorInfo?.id === 'KOHLER' || contractorInfo?.id === 'REECE'

      await Promise.all([
        this.sendMemberInvitation(id, projectId, firstName ?? name, contractorInfo?.name || '', isEnterprise, email),
        this.sendSlackNotification(
          `A new member, ${name}, was invited to project with ID: <${InternalSite.PROJECTS_URL}/${projectId}|${projectId}> by <${InternalSite.CONTRACTORS_URL}/${contractorInfo?.id}|${contractorInfo?.id}>`,
          contractorInfo?.isReal,
          isEnterprise,
        ),
      ])

      return result.rows[0]
    } catch (error) {
      console.error('Database error in addMemberToProject:', error)
      throw new Error('Internal Server Error')
    }
  }

  async addDesignToProject(_projectId: string, _designData: ProjectDesign) {
    console.log('Refusing to add design to project.')
    throw new Error('Designs should be created through the new service now.')
  }

  async updateMember(memberId: string, updateData: MemberUpdateData) {
    const fetchQuery = `SELECT id, status FROM members WHERE id = $1`
    try {
      const fetchResult = await pool.query(fetchQuery, [memberId])
      const existingMember = fetchResult.rows[0]

      if (!existingMember) {
        throw new Error('Member not found')
      }

      const isFirstInvite = existingMember.status === null

      if (isFirstInvite) {
        updateData.status = 'InvitePending'
      }

      const isReinvite = !updateData || !Object.keys(updateData).length
      const preparedData = this.prepareMemberUpdateData(updateData)

      // Convert from `name` attribute to `firstName` and `lastName`
      let firstName: string | null = null
      let lastName: string | null = null
      if (preparedData.name) {
        const [name, ...lastNameParts] = preparedData.name.split(' ')
        firstName = name
        lastName = lastNameParts.length > 0 ? lastNameParts.join(' ') : null

        delete preparedData.name
      }

      const dataToUpdate = CamelCaseConverter.toSnakeCase({ ...preparedData, firstName, lastName })

      const fields = Object.keys(dataToUpdate)
      const values = Object.values(dataToUpdate)

      if (!isReinvite && fields.length === 0) {
        throw new Error('No valid fields provided for update')
      }

      if (isReinvite) {
        fields[0] = 'status'
        values[0] = 'InvitePending'
      }

      const setQuery = fields.map((field, index) => `${field} = $${index + 1}`).join(', ')
      values.push(memberId)

      const updateQuery = `UPDATE members SET ${setQuery} WHERE id = $${values.length} RETURNING *`
      const result = await pool.query(updateQuery, values)
      const member = result.rows[0]

      if (isFirstInvite || isReinvite) {
        let name = member.first_name
        if (member.last_name) {
          name += ` ${member.last_name}`
        }
        const projectId = member.project_id
        const contractorInfo = await this.getContractorInfoByProjectId(projectId)
        const isEnterprise = contractorInfo?.id === 'KOHLER'

        let slackNotificationMessage = `invited to project with ID: <${InternalSite.PROJECTS_URL}/${projectId}|${projectId}> by <${InternalSite.CONTRACTORS_URL}/${contractorInfo?.id}|${contractorInfo?.id}>`
        if (isFirstInvite) {
          slackNotificationMessage = `A new member, ${name}, was ${slackNotificationMessage}`
        } else if (isReinvite) {
          slackNotificationMessage = `An existing member, ${name}, has been re-${slackNotificationMessage}`
        }

        await Promise.all([
          this.sendMemberInvitation(
            member.id,
            projectId,
            member.first_name ?? name,
            contractorInfo?.name || '',
            isEnterprise,
            member.email,
          ),
          this.sendSlackNotification(slackNotificationMessage, contractorInfo?.isReal, isEnterprise),
        ])
      }

      return member
    } catch (error) {
      console.error('Database error:', error)
      throw new Error('Internal Server Error')
    }
  }

  async updateMemberAddOpenPhoneContactId(memberId: string, open_phone_contact_id: string) {
    const updateQuery = `UPDATE members SET open_phone_contact_id = $1 WHERE id = $2`
    const values = [open_phone_contact_id, memberId]
    const result = await pool.query(updateQuery, values)
    const member = result.rows[0]

    return member
  }

  async updateDesign(_projectId: string, _designData: ProjectDesign) {
    console.log('Refusing to update design.')
    throw new Error('Designs should be modified through the new service now.')
  }

  async deleteMember(memberId: string) {
    const client = await pool.connect()

    try {
      await client.query('BEGIN')

      const result = await client.query('DELETE FROM members WHERE id = $1', [memberId])

      if (result.rowCount === 0) {
        throw new Error('Member not found')
      }

      await client.query('COMMIT')
    } catch (error) {
      await client.query('ROLLBACK')
      console.error('Database error in deleteMember:', error)
      throw new Error('Internal Server Error')
    } finally {
      client.release()
    }
  }

  async deleteDesign(_projectId: string, _designId: string) {
    console.log(`Refusing to delete design ${_designId} for project ${_projectId}.`)
    throw new Error('Designs should be deleted through the new service now.')
  }

  async deletePreviewRender(projectId: string, previewId: string, renderId: string) {
    const client = await pool.connect()
    const deleteRenderQuery = `
    WITH deleted_render AS (
      -- Step 1: Delete the render and return its ID.
      -- The WHERE clause ensures you are only deleting the exact render you intend to.
      DELETE FROM renders
      WHERE id = $3
        AND EXISTS (
          SELECT 1
          FROM previews
          WHERE previews.project_id = $1
            AND previews.id = $2
            AND $3 = ANY(previews.render_ids)
        )
      RETURNING id
    )
    -- Step 2: Update the preview's array, removing the ID of the deleted render.
    -- This part only runs if the DELETE above was successful.
    UPDATE previews
    SET render_ids = array_remove(render_ids, (SELECT id FROM deleted_render))
    WHERE id = $2;
    `

    try {
      await client.query('BEGIN')
      const result = await client.query(deleteRenderQuery, [projectId, previewId, renderId])

      if (result.rowCount === 0) {
        throw new Error('Error deleting render: Not found')
      }

      await client.query('COMMIT')
      console.log(`Render deleted successfully with id ${renderId}:`, result.rowCount)
    } catch (error) {
      await client.query('ROLLBACK')
      console.error('Error deleting render:', error)
      throw error
    } finally {
      client.release()
    }
  }

  public async checkForExistingPreview(projectId: string): Promise<boolean> {
    const client = await pool.connect()

    const query = `
      SELECT products->>'clientType' as client_type, active_preview_id
      FROM projects
      WHERE id = $1
    `

    try {
      await client.query('BEGIN')
      const result = await client.query(query, [projectId])
      await client.query('COMMIT')

      const activePreviewId = result.rows[0]?.active_preview_id

      return activePreviewId
    } catch (error) {
      await client.query('ROLLBACK')
      return false
    } finally {
      client.release()
    }
  }

  getMaterialsPackagePricing(designs: ProjectDesign[]) {
    const designsPrices = designs.map(({ totalPrice }) => (totalPrice ?? 0) / 100).filter(Boolean)
    const minPrice = Math.min(...designsPrices)
    const maxPrice = Math.max(...designsPrices)

    const range = maxPrice - minPrice
    const stepSize = range / 5

    let stepCount = 0
    const packagePrices = {
      essential: {
        min: NumberManipulator.floorToNearest(minPrice + stepSize * stepCount++),
        max: NumberManipulator.roundToNearest(minPrice + stepSize * stepCount),
      },
      plus: {
        min: NumberManipulator.roundToNearest(minPrice + stepSize * stepCount++),
        max: NumberManipulator.roundToNearest(minPrice + stepSize * stepCount),
      },
      premium: {
        min: NumberManipulator.roundToNearest(minPrice + stepSize * stepCount++),
        max: NumberManipulator.roundToNearest(minPrice + stepSize * stepCount),
      },
      luxe: {
        min: NumberManipulator.roundToNearest(minPrice + stepSize * stepCount++),
        max: NumberManipulator.ceilToNearest(maxPrice),
      },
    }

    return packagePrices
  }

  async getSummary(filters?: Record<string, string[]>) {
    // TODO: Add more general logic, think about data structure and scale
    const client = await pool.connect()
    try {
      let queryString = `
                SELECT
                    COUNT(projects.id) AS total_projects,
                    SUM(projects.margin_revenue) AS total_revenue,
                    SUM(projects.our_price) AS total_our_price,
                    SUM(projects.customer_price) AS total_customer_price,
                    SUM(CASE WHEN projects.crm_status = 'InProgress' OR projects.crm_status = 'Scanned' THEN 1 ELSE 0 END) AS active_projects,
                    SUM(CASE WHEN projects.crm_status = 'InProgress' THEN 1 ELSE 0 END) AS in_progress_projects,
                    SUM(CASE WHEN projects.crm_status = 'Scanned' THEN 1 ELSE 0 END) AS scanned_projects,
                    SUM(CASE WHEN projects.app_status = 'ReadyToOrder' THEN 1 ELSE 0 END) AS scan_to_pay,
                    ROUND(SUM(CASE WHEN projects.app_status = 'ReadyToOrder' THEN 1 ELSE 0 END) * 100.0 / COUNT(projects.id)) AS scan_to_pay_percentage,
                    SUM(CASE WHEN projects.crm_status = 'Scanned' THEN 1 ELSE 0 END) AS total_scanned_projects,
                    SUM(CASE WHEN projects.crm_status = 'InProgress' THEN 1 ELSE 0 END) AS total_in_progress_projects,
                    SUM(CASE WHEN projects.crm_status = 'ProjectCreated' THEN 1 ELSE 0 END) AS total_project_created_projects,
                    SUM(CASE WHEN projects.crm_status = 'LayoutCaptured' THEN 1 ELSE 0 END) AS total_layout_captured_projects,
                    SUM(CASE WHEN projects.crm_status = 'LayoutApproved' THEN 1 ELSE 0 END) AS total_layout_approved_projects,
                    SUM(CASE WHEN projects.crm_status = 'HomeownerInvited' THEN 1 ELSE 0 END) AS total_homeowner_invited_projects,
                    SUM(CASE WHEN projects.crm_status = 'SurveyCompleted' THEN 1 ELSE 0 END) AS total_survey_completed_projects,
                    SUM(CASE WHEN projects.crm_status = 'HDRendersShared' THEN 1 ELSE 0 END) AS total_hd_renders_shared_projects,
                    SUM(CASE WHEN projects.crm_status = 'DesignKickoff' THEN 1 ELSE 0 END) AS total_design_kickoff_projects,
                    SUM(CASE WHEN projects.crm_status = 'DesignReview' THEN 1 ELSE 0 END) AS total_design_review_projects,
                    SUM(CASE WHEN projects.crm_status = 'Paid' THEN 1 ELSE 0 END) AS total_paid_projects,
                    SUM(CASE WHEN projects.crm_status = 'Ordered' THEN 1 ELSE 0 END) AS total_ordered_projects,
                    SUM(CASE WHEN projects.crm_status = 'Shipped' THEN 1 ELSE 0 END) AS total_shipped_projects,
                    SUM(CASE WHEN projects.crm_status = 'Delivered' THEN 1 ELSE 0 END) AS total_delivered_projects,
                    SUM(CASE WHEN projects.crm_status = 'Paused' THEN 1 ELSE 0 END) AS total_paused_projects,
                    SUM(CASE WHEN projects.crm_status = 'Lost' THEN 1 ELSE 0 END) AS total_lost_projects,
                    SUM(CASE WHEN projects.scan_success = 'not_set' THEN 1 ELSE 0 END) as scan_success_not_set_projects,
                    SUM(CASE WHEN projects.scan_success = 'success' THEN 1 ELSE 0 END) as scan_success_success_projects,
                    SUM(CASE WHEN projects.scan_success = 'technical_error' THEN 1 ELSE 0 END) as scan_success_technical_error_projects,
                    SUM(CASE WHEN projects.scan_success = 'user_error' THEN 1 ELSE 0 END) as scan_success_user_error_projects
                FROM
                    projects
                LEFT JOIN contractors ON projects.contractor_id = contractors.id
            `

      const { whereClause, parameters } = this.paginateService.buildWhereClause(
        filters ? this.formatFilterValues(filters) : {},
        this.columnTableMapping,
        ProjectService.getFilterNameResolver,
      )
      if (whereClause) {
        queryString += ` WHERE ${whereClause};`
      }

      const res = await client.query(queryString, parameters)
      const results = res.rows[0]
      const numeralResults = this.transformResponseNumericValues(results)
      return CamelCaseConverter.toCamelCase(numeralResults)
    } catch (error) {
      console.error('Database error in getSummary:', error)
      throw new Error(`Internal error in getSummary ${error}`)
    } finally {
      client.release()
    }
  }

  private generateDefaultProjectName() {
    const today = new Date()
    const monthNames = [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December',
    ]

    const day = today.getDate()
    let daySuffix

    if (day === 1 || day === 21 || day === 31) {
      daySuffix = 'st'
    } else if (day === 2 || day === 22) {
      daySuffix = 'nd'
    } else if (day === 3 || day === 23) {
      daySuffix = 'rd'
    } else {
      daySuffix = 'th'
    }

    return `${monthNames[today.getMonth()]} ${day}${daySuffix} Scan`
  }

  private prepareProjectUpdateData(updateData: ProjectUpdateData): Partial<ProjectUpdateData> {
    const validFields = new Set<keyof ProjectUpdateData>([
      'appStatus',
      'address',
      'scan',
      'rawScan',
      'products',
      'measurements',
      'name',
      'materials',
      'scanNotes',
      'percentageToClose',
      'notes',
      'closingMonth',
      'isReal',
      'scanSuccess',
      'crmStatus',
      'crmSubstatus',
      'designQuiz',
      'designQuizResult',
      'initialLayout',
      'initialVisualization',
    ])
    const filteredUpdateData: Partial<ProjectUpdateData> = {}

    for (const [key, value] of Object.entries(updateData)) {
      if (!validFields.has(key as keyof ProjectUpdateData)) {
        continue
      }

      if (
        key === 'scan' ||
        key === 'rawScan' ||
        key === 'measurements' ||
        key === 'products' ||
        key === 'materials' ||
        key === 'scanNotes' ||
        key === 'notes' ||
        key === 'designQuiz'
      ) {
        // Ensure value is an object before stringifying
        if (typeof value === 'object') {
          filteredUpdateData[key] = value !== null ? JSON.stringify(value) : null
        }
      } else {
        if (typeof value === 'boolean' && key === 'isReal') {
          filteredUpdateData[key] = value
        }
        if (key === 'scanSuccess' && this.validateScanSuccessValue(value)) {
          filteredUpdateData[key] = value
        }

        if (key === 'crmSubstatus' && this.validateCrmSubstatus(value)) {
          filteredUpdateData[key] = value
        }

        if (key === 'designQuizResult') {
          filteredUpdateData[key] = value as DesignQuizResult
        }

        // For other fields, we expect them to be string or undefined
        if ((typeof value === 'string' || typeof value === 'undefined') && key !== 'scanSuccess') {
          filteredUpdateData[
            key as keyof Omit<ProjectUpdateData, 'isReal' | 'scanSuccess' | 'crmSubstatus' | 'designQuizResult'>
          ] = value
        }
      }
    }

    return filteredUpdateData
  }

  private prepareMemberUpdateData(updateData: MemberUpdateData): Partial<MemberUpdateData> {
    const validFields = new Set<keyof MemberUpdateData>(['name', 'email', 'phone', 'status'])
    const filteredUpdateData: Partial<MemberUpdateData> = {}

    for (const [key, value] of Object.entries(updateData)) {
      if (!validFields.has(key as keyof MemberUpdateData)) {
        continue
      }

      if (typeof value === 'string' || typeof value === 'undefined') {
        filteredUpdateData[key as keyof MemberUpdateData] = value
      }
    }

    return filteredUpdateData
  }

  private async checkIfRealContractor(contractorId: string): Promise<boolean> {
    try {
      const contractorQuery = 'SELECT is_real FROM contractors WHERE id = $1'
      const contractorResult = await pool.query(contractorQuery, [contractorId])

      if (contractorResult.rows.length > 0) {
        return contractorResult.rows[0].is_real
      }
      return false // Default to false if contractor not found
    } catch (error) {
      console.error('Database error in checkIfRealContractor:', error)
      throw new Error('Internal Server Error')
    }
  }

  private async getContractorInfoByProjectId(projectId: string) {
    try {
      const query = `
                SELECT c.id, c.name, c.brand_image_url, c.is_real, c.contact_name
                FROM contractors c
                JOIN projects p ON c.id = p.contractor_id
                WHERE p.id = $1`
      const values = [projectId]
      const result = await pool.query(query, values)

      if (result.rows.length > 0) {
        return CamelCaseConverter.toCamelCase(result.rows[0])
      }
      return null
    } catch (error) {
      console.error('Database error in getContractorInfoByProjectId:', error)
      throw new Error('Internal Server Error')
    }
  }

  private async sendMemberInvitation(
    memberId: string,
    projectId: string,
    memberName: string,
    contractorName: string,
    isEnterprise: boolean,
    email?: string,
    // phone?: string,
  ) {
    // const branchLink = await this.getBranchURL(projectId, memberId)

    if (email) {
      const designQuizLink = `https://app.arcstudio.ai/survey?projectId=${projectId}`
      const emailSubject = 'Let’s Design Your Bathroom'
      let emailBody = `Hi ${capitalize(
        memberName,
      )},<br /><br />Great meeting you today. To receive a materials estimate for your bathroom and discover your design style, 
      please answer a couple questions <a href="${designQuizLink}">here</a>.<br /><br />
      This will help us craft initial designs tailored to your budget and bring your dream bathroom to life. 
      We look forward to helping you with your project!<br /><br />Thank you!<br />${contractorName}`

      if (isEnterprise) {
        emailBody += ' x Bond Studio'
      }

      this.sendEmail(email, contractorName, emailSubject, emailBody, isEnterprise)
    }

    // if (phone) {
    //   this.sendSMS(
    //     phone,
    //     `Get the app to design and order your bathroom materials here: arcstudio://project?projectId=${projectId}&userId=${id}`
    //   );
  }

  private async sendEmail(
    to: string,
    fromName: string,
    subject: string,
    html: string,
    isEnterprise: boolean,
  ): Promise<void> {
    if (!process.env.SENDGRID_API_KEY) {
      return
    }

    sgMail.setApiKey(process.env.SENDGRID_API_KEY)

    const email = isEnterprise ? '<EMAIL>' : '<EMAIL>'
    const from = fromName ? { email, name: fromName } : email

    const msg = {
      to,
      from,
      subject,
      html,
    }

    try {
      await sgMail.send(msg)
      console.log('Email sent successfully')
    } catch (error) {
      console.error('Email sending error:', error)
    }
  }

  private async sendSMS(to: string, body: string): Promise<void> {
    try {
      const client = twilio(process.env.TWILIO_ACCOUNT_SID, process.env.TWILIO_AUTH_TOKEN)

      await client.messages.create({
        body,
        from: '+***********', // Twilio number
        to,
      })
      console.log('SMS sent successfully')
    } catch (error) {
      console.error('SMS sending error:', error)
    }
  }

  private async sendSlackNotificationV2(
    message: string,
    threadId: string,
    isRealUser: boolean,
    isEnterprise: boolean,
    completion?: (threadId: string) => Promise<void>,
  ) {
    const channelId = isEnterprise
      ? process.env.ENTERPRISE_NOTIFICATIONS_SLACK_CHANNEL
      : isRealUser
      ? process.env.APP_NOTIFICATIONS_SLACK_CHANNEL
      : process.env.TEST_NOTIFICATIONS_SLACK_CHANNEL

    try {
      const response = await axios.post(
        'https://slack.com/api/chat.postMessage',
        {
          text: message,
          channel: channelId,
          thread_ts: threadId,
          mrkdwn: true,
        },
        {
          headers: {
            Authorization: `Bearer ${process.env.SLACK_API_KEY}`,
            'Content-Type': 'application/json',
          },
        },
      )
      console.log('Slack sent successfully')
      if (completion) {
        await completion(response.data.ts) // Await the completion callback if provided
      }
      return response.data.ts
    } catch (error) {
      console.error('Error sending Slack notification:', error)
    }
  }

  private async sendSlackNotification(message: string, isRealUser: boolean, isEnterprise: boolean) {
    const webhookUrl = isEnterprise
      ? ENTERPRISE_NOTIFICATIONS_SLACK_WEBHOOK_URL
      : isRealUser
      ? process.env.APP_NOTIFICATIONS_SLACK_WEBHOOK_URL
      : process.env.TEST_NOTIFICATIONS_SLACK_WEBHOOK_URL

    if (!webhookUrl) {
      return
    }

    try {
      await axios.post(webhookUrl, {
        text: message,
      })
      console.log('Slack sent successfully')
    } catch (error) {
      console.error('Error sending Slack notification:', error)
    }
  }

  // private async getBranchURL(projectId: string, memberId: string): Promise<{ url: string }[]> {
  //   try {
  //     const requestBody = {
  //       branch_key: process.env.BRANCH_KEY,
  //       data: {
  //         projectId: projectId,
  //         userId: memberId,
  //       },
  //     }

  //     const config = {
  //       headers: {
  //         'Content-Type': 'application/json',
  //       },
  //     }
  //     const branchURL = 'https://api2.branch.io/v1/url'
  //     const response = await axios.post(branchURL, requestBody, config)

  //     return response.data.url
  //   } catch (error) {
  //     console.error('Error calling Branch API:', error)
  //     throw new Error('Failed to generate branch URL')
  //   }
  // }

  private getIncludesFields(include: string[], baseFields: string[]): string {
    if (!include.length) {
      return baseFields.join(', ')
    }

    const invalidIncludes = include.filter((field) => !projectsGetIncludes.has(field))
    if (invalidIncludes.length) {
      throw new Error(
        `The provided include(s) [${invalidIncludes.join(', ')}] in the query parameter is/are not valid.`,
      )
    }

    const additionalFields = [
      ...baseFields,
      ...include.flatMap((field) => this.setColumnsBasedOnFields(field).map((subField) => `projects.${subField}`)),
    ]

    return additionalFields.join(', ')
  }

  private setColumnsBasedOnFields(include: string): string[] {
    switch (include) {
      case 'margin_info':
        return ['customer_price', 'our_price', 'margin_revenue', 'margin_percentage']
      default:
        return []
    }
  }

  private calculateCustomerPrice(materialsList: Material[]): number {
    return materialsList.reduce((acc: number, material: Material) => acc + (material.price || 0), 0)
  }

  private calculateOurPrice(materialsList: Material[]): number {
    return materialsList.reduce((acc, material) => {
      let availableFromPrice = 0
      if (material.availableFrom) {
        availableFromPrice = material.availableFrom[0].price
      }
      return acc + availableFromPrice
    }, 0)
  }

  private calculateMarginRevenue(customerPrice: number, ourPrice: number): number {
    return customerPrice - ourPrice
  }

  private calculateMarginPercentage(customerPrice: number, marginRevenue: number): number {
    return customerPrice ? NumberManipulator.roundPercentage((marginRevenue / customerPrice) * 100, 0) : 0
  }

  private validateScanSuccessValue = (value: any): value is ScanSuccess => {
    return Object.values(ScanSuccess).includes(value)
  }

  private validateCrmSubstatus = (value: any): value is CRMSubstatusEnum => {
    return Object.values(CRMSubstatusEnum).includes(value)
  }

  private static getFilterNameResolver(key: string): string {
    const keyMap: Record<string, string> = {
      is_project_real: 'is_real',
    }

    return keyMap[key] || key
  }
}

type IssueData = {
  issueText?: string
}

type ProjectData = {
  id?: string
  appStatus?: string
  crmStatus?: string
  name?: string
  address?: string
  scan: any
  rawScan: any
  measurements: any
  products: any
  designs: any
  materials: any
  contractorId: string
  scanNotes?: object | string
  notes?: object | string
  percentageToClose?: number | string
  closingMonth?: number | string
  numberOfScans?: number | string
  isReal?: boolean
  scanSuccess?: ScanSuccess
  customerFirstName?: string
  customerLastName?: string
  crmSubstatus?: CRMSubstatusEnum
  customerEmail?: string
  customerPhone?: string
  designQuiz?: object | string | null
  initialLayout?: string | null
  initialVisualization?: string | null
  designQuizResult?: DesignQuizResult | null
}

export class DesignQuizResult {
  status: DesignQuizResultStatus
  hash: string
  title?: string
  description?: string

  constructor(
    hash: string,
    status: DesignQuizResultStatus = DesignQuizResultStatus.GENERATING,
    payload?: string | Omit<DesignQuizResult, 'status' | 'hash'>,
  ) {
    let designResult: DesignQuizResult = { status, hash }

    if (typeof payload === 'string') {
      designResult = { ...designResult, ...JSON.parse(payload) }
    } else if (payload) {
      designResult = { ...designResult, ...payload }
    }

    this.status = designResult.status
    this.hash = designResult.hash
    this.title = designResult.title
    this.description = designResult.description
  }
}

type ProjectUpdateData = {
  appStatus?: string
  crmStatus?: string
  name?: string
  address?: string
  scan?: object | string | null
  rawScan?: object | string | null
  measurements?: object | string | null
  products?: object | string | null
  materials?: Material[] | string | null
  scanNotes?: object | string | null
  notes?: object | string | null
  percentageToClose?: number | string
  closingMonth?: number | string
  numberOfScans?: number | string
  isReal?: boolean
  scanSuccess?: ScanSuccess
  customerFirstName?: string
  customerLastName?: string
  crmSubstatus?: CRMSubstatusEnum
  customerEmail?: string
  customerPhone?: string
  designQuiz?: object | string | null
  initialLayout?: string | null
  initialVisualization?: string | null
  designQuizResult?: DesignQuizResult | null
}

interface Material {
  price: number
  availableFrom: any[]
}

type ProjectInfo = ProjectData & {
  contractor: any
  members: MemberUpdateData[]
  previews?: Previews
}

type MemberUpdateData = {
  id?: string
  status?: string
  name?: string
  email?: string
  phone?: string
}

type Render = {
  url: string
  title: string
  designer: string
  designerImage: string
  tags: string[]
  description: string
  price: number
  skus: number
  status: string
  actions?: ApiActions
  notes?: string
}

type Preview = {
  status: string
  percentageComplete: number
  shouldDisplayGCInfo: boolean
  created: string
  updated: string
  renders: Render[]
}

type Previews = {
  active: Preview | null
  previous: Preview[]
}

type UpdatePreviewRender = Pick<Render, 'status' | 'price' | 'notes' | 'url'>

type ApiActions = {
  [key in keyof typeof HttpMethods]: `/${string}`
}

export enum DesignQuizResultStatus {
  GENERATING,
  COMPLETED,
  ERROR,
}
