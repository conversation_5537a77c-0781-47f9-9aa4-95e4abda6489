import pool from '../www/index'
import CamelCaseConverter from './CamelCaseConverter'
import { PaginatedDataFetcher } from '../data-adapters'
import { BaseService } from './_BaseService'
import { Customer } from './models/Project'

export class ContractorsService extends BaseService {
  paginateService: PaginatedDataFetcher

  constructor() {
    super()
    this.paginateService = new PaginatedDataFetcher('contractors')
  }

  getAllContractors = async (
    offset?: number | any,
    pageSize?: number | any,
    filters?: Record<string, any[]>,
    orderBy?: string,
    sortBy?: string,
  ) => {
    try {
      const result = await this.paginateService.executeFetch(
        '*',
        filters,
        '',
        offset,
        pageSize,
        undefined,
        sortBy,
        orderBy,
      )
      return result
    } catch (error) {
      console.error('Database error:', error)
      throw new Error('Internal Server Error')
    }
  }

  getProjectsResults = async (contractorId: string) => {
    try {
      const projectsQuery =
        'SELECT id, appStatus, crmStatus, name, address, scan, measurements, products, designs, materials FROM projects WHERE contractor_id = $1'
      const projectsResult = await pool.query(projectsQuery, [contractorId])
      return projectsResult.rows
    } catch (error) {
      console.error('Database error:', error)
      throw new Error('Internal Server Error')
    }
  }

  getContractorById = async (contractorIds: string[]) => {
    try {
      const contractorsQuery = `
                    *,
                    (
                        SELECT json_agg(p)
                        FROM (
                            SELECT
                                id,
                                app_status,
                                crm_status,
                                name,
                                address,
                                scan,
                                measurements,
                                products,
                                designs,
                                materials,
                                (
                                    SELECT json_agg(m)
                                    FROM (
                                        SELECT
                                            id,
                                            CONCAT(first_name, ' ', last_name) as name,
                                            email,
                                            phone,
                                            status
                                        FROM members m
                                        WHERE m.project_id = p.id
                                    ) m
                                ) AS members
                            FROM projects p
                            WHERE p.contractor_id = contractors.id
                        ) p
                    ) AS projects
            `

      const filters = {
        id: contractorIds,
      }

      const contractorResult = await this.paginateService.executeFetch(
        contractorsQuery,
        filters,
        '',
        1,
        contractorIds.length,
      )

      if (contractorResult.data.length === 0) {
        return null
      }

      const contractorDataTransform = contractorResult.data.map((contractor) => {
        const contractorToCamelCase: ContractorType = CamelCaseConverter.toCamelCase(contractor)
        const project =
          contractor?.projects?.map((project: any) => {
            const p = CamelCaseConverter.toCamelCase(project)
            const members = project.members?.map((member: any) => CamelCaseConverter.toCamelCase(member)) || []

            const [firstName, ...lastNameParts] = members[0]?.name.split(' ') || []
            const lastName = lastNameParts.length > 0 ? lastNameParts.join(' ') : undefined
            p.customerFirstName = firstName
            p.customerLastName = lastName
            p.customerEmail = members[0]?.email
            p.customerPhone = members[0]?.phone
            //KD jan-17-25: Adding both shapes for customer info. After releasing the app, we can coalesce around just this one and remove the 4 lines above
            p.customer = new Customer({
              firstName: firstName,
              lastName: lastName,
              email: members[0]?.email,
              phone: members[0]?.phone,
            })

            return {
              ...p,
              members: members,
            }
          }) || []

        return {
          ...contractorToCamelCase,
          projects: project,
        }
      })

      return {
        ...contractorResult,
        data: contractorDataTransform,
      }
    } catch (error) {
      console.error('Database error:', error)
      throw new Error('Internal Server Error')
    }
  }

  createNewContractor = async (contractorData: ContractorType) => {
    try {
      let { id, brandImageUrl, name, isReal, contactName, contactAddress, contactEmail, contactPhone } = contractorData

      id = id.toUpperCase()

      const newContractorQuery = `
            INSERT INTO contractors (
                id,
                brand_image_url,
                name,
                is_real,
                contact_name,
                contact_address,
                contact_email,
                contact_phone
            ) VALUES (
                $1, $2, $3, $4, $5, $6, $7, $8
            ) RETURNING *`
      const newContractorResult = await pool.query(newContractorQuery, [
        id,
        brandImageUrl,
        name,
        isReal,
        contactName,
        contactAddress,
        contactEmail,
        contactPhone,
      ])
      if (newContractorResult.rows.length === 0) {
        return null
      }
      return newContractorResult.rows[0]
    } catch (error: any) {
      const errorMessage = error.detail
      console.error('Database error:', error)
      throw new Error(errorMessage)
    }
  }

  patchContractor = async (contractorId: string, contractorData: ContractorFromDB) => {
    try {
      const contractorDataToCamelCase = CamelCaseConverter.toSnakeCase(contractorData)
      const updateFields = Object.keys(contractorDataToCamelCase)
      const updateValues = Object.values(contractorDataToCamelCase)
      const setFields = updateFields.map((field, index) => `${field} = $${index + 2}`).join(', ')
      const updateContractorQuery = `
                UPDATE contractors
                SET ${setFields}
                WHERE id = $1
                RETURNING *
            `
      const updateContractorResult = await pool.query(updateContractorQuery, [contractorId, ...updateValues])
      if (updateContractorResult.rows.length === 0) {
        return null
      }
      return updateContractorResult.rows[0]
    } catch (error: any) {
      const errorMessage = error.detail
      console.error('Database error:', error)
      throw new Error(errorMessage)
    }
  }

  updateContractorContactId = async (contractorId: string, openPhoneContactId: string) => {
    try {
      const updateContractorQuery = `
                UPDATE contractors
                SET open_phone_contact_id = $1
                WHERE id = $2
                RETURNING *
            `
      const updateContractorResult = await pool.query(updateContractorQuery, [openPhoneContactId, contractorId])
      if (updateContractorResult.rows.length === 0) {
        return null
      }
      return updateContractorResult.rows[0]
    } catch (error: any) {
      const errorMessage = error.detail
      console.error('Database error:', error)
      throw new Error(errorMessage)
    }
  }

  deleteContractor = async (contractorId: string) => {
    try {
      const projectsResult = await this.getProjectsResults(contractorId)
      if (projectsResult?.length > 0) {
        throw {
          detail: 'Cannot delete contractor that has projects',
          code: 400,
        }
      }

      const deleteContractorQuery = 'DELETE FROM contractors WHERE id = $1 RETURNING *'
      const deletedContractorResult = await pool.query(deleteContractorQuery, [contractorId])

      if (deletedContractorResult.rowCount === 0) {
        return null
      }

      return deletedContractorResult.rows[0]
    } catch (error: any) {
      const errorMessage = error.detail
      console.error('Database error:', error)
      throw new Error(errorMessage)
    }
  }

  async getSummary(filters?: Record<string, string[]>) {
    // TODO: Add more general logic, think about data structure and scale
    const client = await pool.connect()
    try {
      let queryString = `
                SELECT
                    COUNT(projects.id) AS total_projects,
                    SUM(projects.margin_revenue) AS total_revenue,
                    SUM(projects.our_price) AS total_our_price,
                    SUM(projects.customer_price) AS total_customer_price,
                    SUM(CASE WHEN projects.crm_status = 'InProgress' OR projects.crm_status = 'Scanned' THEN 1 ELSE 0 END) AS active_projects,
                    SUM(CASE WHEN projects.crm_status = 'InProgress' THEN 1 ELSE 0 END) AS in_progress_projects,
                    SUM(CASE WHEN projects.crm_status = 'Scanned' THEN 1 ELSE 0 END) AS scanned_projects,
                    SUM(CASE WHEN projects.app_status = 'ReadyToOrder' THEN 1 ELSE 0 END) AS scan_to_pay,
                    ROUND(SUM(CASE WHEN projects.app_status = 'ReadyToOrder' THEN 1 ELSE 0 END) * 100.0 / CASE WHEN COUNT(projects.id) = 0 THEN 1 ELSE COUNT(projects.id) END) AS scan_to_pay_percentage,
                    SUM(CASE WHEN projects.crm_status = 'Scanned' THEN 1 ELSE 0 END) AS total_scanned_projects,
                    SUM(CASE WHEN projects.crm_status = 'InProgress' THEN 1 ELSE 0 END) AS total_in_progress_projects,
                    SUM(CASE WHEN projects.crm_status = 'ProjectCreated' THEN 1 ELSE 0 END) AS total_project_created_projects,
                    SUM(CASE WHEN projects.crm_status = 'LayoutCaptured' THEN 1 ELSE 0 END) AS total_layout_captured_projects,
                    SUM(CASE WHEN projects.crm_status = 'LayoutApproved' THEN 1 ELSE 0 END) AS total_layout_approved_projects,
                    SUM(CASE WHEN projects.crm_status = 'HomeownerInvited' THEN 1 ELSE 0 END) AS total_homeowner_invited_projects,
                    SUM(CASE WHEN projects.crm_status = 'SurveyCompleted' THEN 1 ELSE 0 END) AS total_survey_completed_projects,
                    SUM(CASE WHEN projects.crm_status = 'HDRendersShared' THEN 1 ELSE 0 END) AS total_hd_renders_shared_projects,
                    SUM(CASE WHEN projects.crm_status = 'DesignKickoff' THEN 1 ELSE 0 END) AS total_design_kickoff_projects,
                    SUM(CASE WHEN projects.crm_status = 'DesignReview' THEN 1 ELSE 0 END) AS total_design_review_projects,
                    SUM(CASE WHEN projects.crm_status = 'Paid' THEN 1 ELSE 0 END) AS total_paid_projects,
                    SUM(CASE WHEN projects.crm_status = 'Ordered' THEN 1 ELSE 0 END) AS total_ordered_projects,
                    SUM(CASE WHEN projects.crm_status = 'Shipped' THEN 1 ELSE 0 END) AS total_shipped_projects,
                    SUM(CASE WHEN projects.crm_status = 'Delivered' THEN 1 ELSE 0 END) AS total_delivered_projects,
                    SUM(CASE WHEN projects.crm_status = 'Paused' THEN 1 ELSE 0 END) AS total_paused_projects,
                    SUM(CASE WHEN projects.crm_status = 'Lost' THEN 1 ELSE 0 END) AS total_lost_projects,
                    SUM(CASE WHEN projects.scan_success = 'not_set' THEN 1 ELSE 0 END) as scan_success_not_set_projects,
                    SUM(CASE WHEN projects.scan_success = 'success' THEN 1 ELSE 0 END) as scan_success_success_projects,
                    SUM(CASE WHEN projects.scan_success = 'technical_error' THEN 1 ELSE 0 END) as scan_success_technical_error_projects,
                    SUM(CASE WHEN projects.scan_success = 'user_error' THEN 1 ELSE 0 END) as scan_success_user_error_projects,
                    SUM(CASE WHEN (TIMESTAMP '2001-01-01' + INTERVAL '1 second' * ROUND((projects.scan->>'scannedDate')::numeric)) >= (CURRENT_DATE - INTERVAL '30 days') THEN 1 WHEN projects.app_status = 'ReadyToOrder' THEN 1 ELSE 0 END) AS active_contractors,
                    SUM(CASE WHEN EXTRACT(MONTH FROM contractors.created) = EXTRACT(MONTH FROM CURRENT_DATE) AND EXTRACT(YEAR FROM contractors.created) = EXTRACT(YEAR FROM CURRENT_DATE) THEN 1 ELSE 0 END) AS mtd_contractors
                FROM
                    contractors
                LEFT JOIN projects ON contractors.id = projects.contractor_id
            `

      const { whereClause, parameters } = this.paginateService.buildWhereClause(
        filters ? this.formatFilterValues(filters) : {},
        'contractors',
      )

      if (whereClause) {
        queryString += ` WHERE ${whereClause};`
      }

      const res = await client.query(queryString, parameters)
      const results = res.rows[0]
      const numeralResults = this.transformResponseNumericValues(results)
      return CamelCaseConverter.toCamelCase(numeralResults)
    } catch (error) {
      console.error('Database error in getSummary:', error)
      throw new Error(`Internal error in getSummary ${error}`)
    } finally {
      client.release()
    }
  }
}

interface ContractorFromDB {
  id: string
  brand_image_url: string
  name: string
  is_real: boolean
  contact_name: string
  contact_email: string
  contact_phone: number
  contact_address: string
  open_phone_contact_id: string
}

interface ContractorType {
  id: string
  brandImageUrl: string
  name: string
  isReal: boolean
  contactName: string
  contactEmail: string
  contactPhone: string
  contactAddress: string
}
