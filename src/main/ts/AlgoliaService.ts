import { buildProjectsResponse } from '../v2/handlers/projects'
import { searchIndices } from '../www'
import { ContractorsService } from './ContractorsService'
import { ProjectService } from './ProjectsService'

const searchIndexVersion = 2
const projectService = new ProjectService()
const contractorService = new ContractorsService()

export default class AlgoliaService {
  static FETCH_PER_PAGE = 100
  static INITIAL_PAGE = 1

  // PROJECTS
  public static async addProjectsToSearchIndex(): Promise<void> {
    try {
      if (searchIndices) {
        const { data: projects, pagination } = await projectService.getAllProjects(
          this.FETCH_PER_PAGE,
          this.INITIAL_PAGE,
        )

        const records = projects.map(AlgoliaService.convertProjectRecord)
        await searchIndices.projects_search_index.saveObjects(records)

        if (pagination && pagination.lastPage > this.INITIAL_PAGE) {
          for (let i = this.INITIAL_PAGE + 1; i <= pagination.lastPage; i++) {
            const { data: nextProjects } = await projectService.getAllProjects(
              this.FETCH_PER_PAGE,
              i,
              undefined,
              undefined,
              undefined,
              ['margin_info'],
            )
            const nextRecords = nextProjects.map(AlgoliaService.convertProjectRecord)
            await searchIndices.projects_search_index.saveObjects(nextRecords)
          }
        }
      }
    } catch (e) {
      console.error(e)
    }
  }

  public static async addProjectToSearchIndex(project: Record<string, any>, partial = false) {
    try {
      if (searchIndices) {
        const record = AlgoliaService.convertProjectRecord(project)

        await (partial
          ? searchIndices.projects_search_index.partialUpdateObject(record)
          : searchIndices.projects_search_index.saveObject(record))
      } else {
        console.error(`Project with id ${project.id} has does not exist, unable to update search index`)
      }
    } catch (e) {
      console.error(e)
    }
  }

  public static async removeProjectFromSearchIndex(projectId: string) {
    try {
      if (searchIndices) {
        await searchIndices.projects_search_index.deleteObject(projectId)
      }
    } catch (e) {
      console.error(e)
    }
  }

  // CONTRACTORS
  public static async addContractorsToSearchIndex(): Promise<void> {
    try {
      if (searchIndices) {
        const { data: contractors, pagination } = await contractorService.getAllContractors(
          this.INITIAL_PAGE,
          this.FETCH_PER_PAGE,
        )

        const records = contractors.map(AlgoliaService.convertContractorRecord)
        await searchIndices.contractors_search_index.saveObjects(records)

        if (pagination && pagination.lastPage > this.INITIAL_PAGE) {
          for (let i = this.INITIAL_PAGE + 1; i <= pagination.lastPage; i++) {
            const { data: nextContractors } = await contractorService.getAllContractors(i, this.FETCH_PER_PAGE)
            const nextRecords = nextContractors.map(AlgoliaService.convertContractorRecord)

            await searchIndices.contractors_search_index.saveObjects(nextRecords)
          }
        }
      }
    } catch (e) {
      console.error(e)
    }
  }

  public static async addContractorToSearchIndex(contractor: Record<string, any>) {
    try {
      if (searchIndices) {
        const record = AlgoliaService.convertContractorRecord(contractor)
        await searchIndices.contractors_search_index.saveObject(record)
      } else {
        console.error(`Project with id ${contractor.id} has does not exist, unable to update search index`)
      }
    } catch (e) {
      console.error(e)
    }
  }

  public static async removeContractorFromSearchIndex(contractorId: string) {
    try {
      if (searchIndices) {
        await searchIndices.contractors_search_index.deleteObject(contractorId)
      }
    } catch (e) {
      console.error(e)
    }
  }

  private static convertProjectRecord(record: Record<string, any>): Record<string, any> {
    const convertedRecord = buildProjectsResponse(record)
    return { objectID: record.id, searchIndexVersion, ...convertedRecord }
  }

  private static convertContractorRecord(record: Record<string, any>): Record<string, any> {
    return { objectID: record.id, searchIndexVersion, ...record }
  }
}
