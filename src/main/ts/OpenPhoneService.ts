import axios from 'axios'
import { parsePhoneNumberFromString } from 'libphonenumber-js'

import { BaseService } from './_BaseService'
import { ContractorsService } from './ContractorsService'
import { ProjectService } from './ProjectsService'

const contractorsService = new ContractorsService()
const projectService = new ProjectService()

interface NumberObject {
  id: string
  name: string
  number: string
  messages: string[]
  memberId?: string
}

export default class OpenPhoneService extends BaseService {
  constructor() {
    super()
  }

  private parseNumber(phoneNumber: string) {
    const parsedNumber = parsePhoneNumberFromString(phoneNumber, 'US')?.format('E.164')
    return parsedNumber
  }

  private async getMessages(participants: string[], member?: any) {
    const formattedParticipantNumbers = participants.map((participant) => {
      const parsedNumber = this.parseNumber(participant)
      const encodedNumber = parsedNumber ? encodeURIComponent(parsedNumber) : ''
      return `participants=${encodedNumber}`
    })

    // make an API call to open phone's api to get all phone numbers for our api key
    const resp = await axios.get('https://api.openphone.com/v1/phone-numbers', {
      headers: {
        Authorization: `${process.env.OPENPHONE_API_KEY}`,
      },
    })
    const responseNumbers = resp.data.data

    const numbersWithMessages: { numbers: NumberObject[]; totalItems: number; nextPageToken: string } = {
      numbers: [],
      totalItems: 0,
      nextPageToken: '',
    }

    // check each number for messages
    for (const number of responseNumbers) {
      const numberObject: NumberObject = {
        id: number.id,
        name: number.name,
        number: number.number,
        messages: [],
      }
      const messagesUrl = `https://api.openphone.com/v1/messages?phoneNumberId=${
        number.id
      }&${formattedParticipantNumbers.join('&')}&maxResults=100`
      const messagesResp = await axios.get(messagesUrl, {
        headers: {
          Authorization: `${process.env.OPENPHONE_API_KEY}`,
        },
      })

      if (messagesResp.data.totalItems === 0) {
        continue
      }
      if (member) {
        numberObject.memberId = member.id
      }
      numberObject.messages = messagesResp.data.data.reverse()
      numbersWithMessages.numbers.push(numberObject)
      numbersWithMessages.totalItems = messagesResp.data.totalItems
      numbersWithMessages.nextPageToken = messagesResp.data.nextPageToken
    }
    return numbersWithMessages
  }

  async getContractorMessages(contractorId: string) {
    // load the contractor
    const contractors = await contractorsService.getContractorById([contractorId])
    const contractor = contractors?.data[0]
    if (!contractor || !contractor.contactPhone) {
      return ['None']
    }

    return await this.getMessages([contractor.contactPhone])
  }

  async getProjectMessages(projectId: string) {
    // load the project
    const projects = await projectService.getProjectById([projectId])
    const project = projects?.data[0]

    if (!project || !project.contractorId) {
      return ['None']
    }

    // the API only works for 1:1 conversations, so cut the contractor for now and do seperate calls for the members
    const phoneNumberReturn = []
    for (const member of project.members) {
      if (!member || !member.phone || !member.id) {
        return
      }
      const result = await this.getMessages([member.phone], member)
      if (result.totalItems > 0) {
        return result
      }
      phoneNumberReturn.push(result)
    }

    return phoneNumberReturn
  }

  async createContactMember(member: any) {
    const data = {
      defaultFields: {
        firstName: member.name.split(' ')[0],
        lastName: member.name.includes(' ') ? member.name.split(' ')[1] : '',
        emails: [
          {
            name: 'Platform email',
            value: member.email,
          },
        ],
        phoneNumbers: [
          {
            name: 'Platform number',
            value: member.phone,
          },
        ],
      },
    }

    const resp = await axios.post('https://api.openphone.com/v1/contacts', data, {
      headers: {
        Authorization: `${process.env.OPENPHONE_API_KEY}`,
        'Content-Type': 'application/json',
      },
    })

    const updatedMember = await projectService.updateMemberAddOpenPhoneContactId(member.id, resp.data.data.id)
    return updatedMember
  }

  async upsertContactContractor(contractor: any) {
    // if contractor doesn't have an open phone contact id, create one
    if (!contractor.open_phone_contact_id) {
      const data = {
        defaultFields: {
          firstName: contractor.name.split(' ')[0],
          lastName: contractor.name.includes(' ') ? contractor.name.split(' ')[1] : '',
          company: contractor.name,
          emails: [
            {
              name: 'Platform email',
              value: contractor.contact_email || contractor.contactEmail,
            },
          ],
          phoneNumbers: [
            {
              name: 'Platform number',
              value: contractor.contact_phone || contractor.contactPhone,
            },
          ],
        },
      }
      // catch axios error and print body
      const resp = await axios.post('https://api.openphone.com/v1/contacts', data, {
        headers: {
          Authorization: `${process.env.OPENPHONE_API_KEY}`,
          'Content-Type': 'application/json',
        },
      })
      const updatedContractor = await contractorsService.updateContractorContactId(contractor.id, resp.data.data.id)

      return updatedContractor
    } else {
      // update it, note that openphone only APPENDS to the listed phonenumbers, you cannot clear it out this way
      const data = {
        defaultFields: {
          emails: [
            {
              name: 'Platform email',
              value: contractor.contact_email || contractor.contactEmail,
            },
          ],
          phoneNumbers: [
            {
              name: 'Platform number',
              value: contractor.contact_phone || contractor.contactPhone,
            },
          ],
        },
      }
      await axios.patch(`https://api.openphone.com/v1/contacts/${contractor.open_phone_contact_id}`, data, {
        headers: {
          Authorization: `${process.env.OPENPHONE_API_KEY}`,
          'Content-Type': 'application/json',
        },
      })
      return contractor
    }
  }

  async textMemberInvitation(member: any, projectId: string) {
    if (!member.phone) {
      console.log('No phone number for member')
      return
    }
    const designQuizLink = `https://app.arcstudio.ai/survey?projectId=${projectId}`

    const projects = await projectService.getProjectById([projectId])
    const project = projects?.data[0]

    if (!project || !project.address) {
      return
    }

    const formattedAddress = project.address.split(',')[0]

    const message = `Hi ${member.name}! To receive an instant materials estimate for your bathroom at ${formattedAddress} and discover your design style, please answer a couple questions <a href="${designQuizLink}">here</a>`
    const data = {
      from: '+19452371550', // open phone number
      to: [this.parseNumber(member.phone)],
      content: message,
    }

    const resp = await axios.post('https://api.openphone.com/v1/messages', data, {
      headers: {
        Authorization: `${process.env.OPENPHONE_API_KEY}`,
        'Content-Type': 'application/json',
      },
    })

    return resp
  }
}
