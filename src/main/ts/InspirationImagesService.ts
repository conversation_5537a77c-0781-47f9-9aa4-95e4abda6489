import pool from '../www/index'
import CamelCaseConverter from './CamelCaseConverter'
import { InspirationImage } from './models/InspirationImage'

class InspirationImagesService {
  async getInspirationImagesById(imageId: string) {
    const client = await pool.connect()

    const queryString = `SELECT * FROM inspiration_images WHERE id = $1`

    try {
      client.query('BEGIN')
      const queryResult = await client.query(queryString, [imageId])
      const rows = queryResult.rows
      const inspirationImages: InspirationImage[] = rows.map((row) => CamelCaseConverter.toCamelCase(row))
      await client.query('COMMIT')
      return inspirationImages
    } catch (error) {
      client.query('ROLLBACK')
      throw error
    } finally {
      client.release()
    }
  }

  async getInspirationImagesByHash(imageHash: string) {
    const client = await pool.connect()

    const queryString = `SELECT * FROM inspiration_images WHERE image_hash = $1`

    try {
      client.query('BEGIN')
      const queryResult = await client.query(queryString, [imageHash])
      const rows = queryResult.rows
      const inspirationImages: InspirationImage[] = rows.map((row) => CamelCaseConverter.toCamelCase(row))
      await client.query('COMMIT')
      return inspirationImages[0]
    } catch (error) {
      client.query('ROLLBACK')
      throw error
    } finally {
      client.release()
    }
  }

  async getInspirationImagesByUrl(decodedUrl: string): Promise<InspirationImage[]> {
    const client = await pool.connect()
    const queryString = `SELECT * FROM inspiration_images WHERE image_url = $1`

    try {
      await client.query('BEGIN')
      const result = await client.query(queryString, [decodedUrl])
      await client.query('COMMIT')

      const rows = result.rows
      const inspirationImages: InspirationImage[] = rows.map((row) => CamelCaseConverter.toCamelCase(row))

      return inspirationImages
    } catch (error) {
      await client.query('ROLLBACK')
      throw error
    } finally {
      client.release()
    }
  }

  async createInspirationImage(newImage: InspirationImage) {
    const client = await pool.connect()

    const queryString = `
          INSERT INTO inspiration_images (
            project_id,
            image_url,
            notes,
            contents,
            ai_description,
            image_hash
          )
          VALUES ($1, $2, $3, $4, $5, $6)
          RETURNING *
        `

    const values = [
      newImage.projectId || null,
      newImage.imageUrl,
      newImage.notes || null,
      newImage.contents ? JSON.stringify(newImage.contents) : null,
      newImage.aiDescription ? JSON.stringify(newImage.aiDescription) : null,
      newImage.imageHash || null,
    ]

    try {
      await client.query('BEGIN')
      const result = await client.query(queryString, values)
      await client.query('COMMIT')
      const [row] = result.rows

      const insertedImage = CamelCaseConverter.toCamelCase(row)

      return insertedImage
    } catch (error) {
      await client.query('ROLLBACK')
      throw error
    } finally {
      client.release()
    }
  }

  async updateInspirationImage(imageId: string, updateFields: Partial<InspirationImage>) {
    const client = await pool.connect()

    const setClauses: string[] = []
    const values: any[] = []
    let idx = 1

    if (typeof updateFields.projectId !== 'undefined') {
      setClauses.push(`project_id = $${idx}`)
      values.push(updateFields.projectId)
      idx++
    }

    if (typeof updateFields.imageUrl !== 'undefined') {
      setClauses.push(`image_url = $${idx}`)
      values.push(updateFields.imageUrl)
      idx++
    }

    if (typeof updateFields.notes !== 'undefined') {
      setClauses.push(`notes = $${idx}`)
      values.push(updateFields.notes)
      idx++
    }

    if (typeof updateFields.contents !== 'undefined') {
      setClauses.push(`contents = $${idx}`)
      values.push(updateFields.contents ? JSON.stringify(updateFields.contents) : null)
      idx++
    }

    if (typeof updateFields.aiDescription !== 'undefined') {
      setClauses.push(`ai_description = $${idx}`)
      values.push(updateFields.aiDescription ? JSON.stringify(updateFields.aiDescription) : null)
      idx++
    }

    if (typeof updateFields.imageHash !== 'undefined') {
      setClauses.push(`image_hash = $${idx}`)
      values.push(updateFields.imageHash)
      idx++
    }

    // If no fields provided, there's nothing to update
    if (setClauses.length === 0) {
      return null
    }

    const queryString = `
          UPDATE inspiration_images
          SET ${setClauses.join(', ')}
          WHERE id = $${idx}
          RETURNING *
        `
    values.push(imageId)

    try {
      await client.query('BEGIN')
      const result = await client.query(queryString, values)
      await client.query('COMMIT')

      if (result.rows.length === 0) {
        // No row was updated, possibly the ID was not found
        return null
      }

      const [row] = result.rows
      const updatedImage = CamelCaseConverter.toCamelCase(row)

      return updatedImage
    } catch (error) {
      await client.query('ROLLBACK')
      throw error
    } finally {
      client.release()
    }
  }

  async deleteInspirationImage(imageId: string) {
    const client = await pool.connect()

    const queryString = `
          DELETE FROM inspiration_images
          WHERE id = $1
          RETURNING *
        `

    try {
      await client.query('BEGIN')
      const result = await client.query(queryString, [imageId])
      await client.query('COMMIT')

      if (result.rows.length === 0) {
        return null
      }

      return CamelCaseConverter.toCamelCase(result.rows[0])
    } catch (error) {
      await client.query('ROLLBACK')
      throw error
    } finally {
      client.release()
    }
  }
}

export default new InspirationImagesService()
