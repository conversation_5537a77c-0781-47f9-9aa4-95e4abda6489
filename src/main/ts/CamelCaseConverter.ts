class CamelCaseConverter {
  static toCamelCase<T = Record<string, any>>(obj: Record<string, any>): T {
    return Object.keys(obj).reduce((result: Record<string, any>, key) => {
      const camelKey = key.replace(/([-_][a-z])/g, (group) => group.toUpperCase().replace('-', '').replace('_', ''))
      result[camelKey] = obj[key]
      return result
    }, {}) as T
  }

  static toSnakeCase(obj: Record<string, any>): Record<string, any> {
    return Object.keys(obj).reduce((result: Record<string, any>, key) => {
      const snakeKey = key.replace(/([A-Z])/g, (group) => `_${group.toLowerCase()}`)
      result[snakeKey] = obj[key]
      return result
    }, {})
  }

  static toSnakeCaseWithException(obj: Record<string, any>, exception: string): Record<string, any> {
    return Object.keys(obj).reduce((result: Record<string, any>, key) => {
      const snakeKey = key.includes(exception) ? key : this.camelToSnakeCase(key)
      result[snakeKey] = obj[key]
      return result
    }, {})
  }

  static camelToSnakeCase(str: string): string {
    return str.replace(/([a-z])([A-Z])/g, '$1_$2').toLowerCase()
  }
}

export default CamelCaseConverter
