export default class SampleRequests {
  public static readonly DESIGN_REQUEST = {
    messages: [
      {
        content: "Hello, I'm <PERSON>",
        role: 'user',
      },
      {
        content:
          "Hi <PERSON>, it's nice to meet you! I'm <PERSON>, your virtual interior design assistant. I'm here to help you create the perfect space. To start, could you describe the style you envision for your bathroom? Are you leaning more towards a modern, traditional, or perhaps a different style?",
        role: 'assistant',
      },
      {
        content: 'Modern',
        role: 'user',
      },
      {
        content:
          "Great choice, Kevin. Modern designs can really open up a room and give it a sleek and stylish feel. Now, can you tell me a little bit about the ambiance you're hoping for in the bathroom? Are you aiming to create a relaxing sanctuary, a functional space, or something else entirely?",
        role: 'assistant',
      },
      {
        content: 'Functional space',
        role: 'user',
      },
      {
        content:
          "Perfect, functionality is key in any space, especially in a bathroom. Now, I'd like to ask about your budget for materials. Have you set aside a specific amount for this, or are you more flexible depending on the materials you choose?",
        role: 'assistant',
      },
      {
        content: '$5000',
        role: 'user',
      },
      {
        content:
          "Thank you for sharing that, <PERSON>. It's helpful to know your budget as we start to consider materials and design options. Lastly, do you have a timeline in mind for this project? When would you ideally like to have everything ready by?",
        role: 'assistant',
      },
      {
        content: '15 weeks',
        role: 'user',
      },
      {
        content: 'Great, lets review your preferences.',
        role: 'assistant',
      },
    ],
    leadTime: 105,
    measurements: {
      wallPaint: {
        SquareFootage: 114.2,
        WidthInches: 175.2,
        HeightInches: 187.8,
      },
      floor: {
        SquareFootage: 50.9,
        WidthInches: 106.1,
        HeightInches: 69.1,
      },
      vanityWall: {
        SquareFootage: 69.1,
        WidthInches: 106.1,
        HeightInches: 93.9,
      },
      showerWall: {
        SquareFootage: 45.1,
        WidthInches: 69.1,
        HeightInches: 93.9,
      },
      vanityLength: 24,
      totalWall: {
        SquareFootage: 228.4,
        WidthInches: 350.4,
        HeightInches: 375.6,
      },
    },
    budget: 5000,
  }
}
