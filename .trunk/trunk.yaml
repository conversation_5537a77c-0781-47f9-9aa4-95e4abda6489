# This file controls the behavior of Trunk: https://docs.trunk.io/cli
# To learn more about the format of this file, see https://docs.trunk.io/reference/trunk-yaml
version: 0.1
cli:
  version: 1.24.0
# Trunk provides extensibility via plugins. (https://docs.trunk.io/plugins)
plugins:
  sources:
    - id: trunk
      ref: v1.7.0
      uri: https://github.com/trunk-io/plugins
# Many linters and tools depend on runtimes - configure them here. (https://docs.trunk.io/runtimes)
runtimes:
  enabled:
    - go@1.21.0
    - node@22.16.0
    - python@3.10.8
# This is the section where you manage your linters. (https://docs.trunk.io/check/configuration)
lint:
  enabled:
    - bandit@1.8.3
    - black@25.1.0
    - checkov@3.2.435
    - eslint@8.57.0
    - git-diff-check
    - gofmt@1.20.4
    - golangci-lint2@2.1.6
    - hadolint@2.12.1-beta
    - isort@6.0.1
    - markdownlint@0.45.0
    - osv-scanner@2.0.2
    - prettier@3.5.3
    - ruff@0.11.12
    - shellcheck@0.10.0
    - shfmt@3.6.0
    - trufflehog@3.88.35
    - yamllint@1.37.1
actions:
  disabled:
    - trunk-announce
    - trunk-check-pre-push
    - trunk-fmt-pre-commit
  enabled:
    - trunk-upgrade-available
