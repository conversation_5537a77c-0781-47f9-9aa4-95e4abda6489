version: 0.2

env:
  variables:
    REPOSITORY_URI: '684617905441.dkr.ecr.us-west-2.amazonaws.com/bathroom-designer-backend:latest'
    CONTAINER_NAME: 'product-picker'

phases:
  install:
    runtime-versions:
      nodejs: 18
    commands:
      - echo Installing dependencies...
      - npm install

  pre_build:
    commands:
      - echo Logging in to Amazon ECR...
      - $(aws ecr get-login --no-include-email --region us-west-2)

  build:
    commands:
      - echo Building the Docker image...
      - docker build -t $REPOSITORY_URI:latest .
      - docker tag $REPOSITORY_URI:latest $REPOSITORY_URI:$CODEBUILD_RESOLVED_SOURCE_VERSION

  post_build:
    commands:
      - echo Pushing Docker image to ECR...
      - docker push $REPOSITORY_URI:$CODEBUILD_RESOLVED_SOURCE_VERSION
      - echo '[{"name":"'$CONTAINER_NAME'","imageUri":"'$REPOSITORY_URI:$CODEBUILD_RESOLVED_SOURCE_VERSION'"}]' > imagedefinitions.json

artifacts:
  files: imagedefinitions.json
